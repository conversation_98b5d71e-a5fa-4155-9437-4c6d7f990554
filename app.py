#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ===============================================
# IMPORT ORGANIZZATI PER CONFORMITÀ PEP 8
# ===============================================

# Standard library imports
import asyncio
import json
import logging
import math
import os
import shutil
import socket
import sys
import time
import traceback
import uuid
import warnings
from datetime import datetime, date, timedelta

# Third-party imports
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session, make_response
from flask.json.provider import JSONProvider
from flask_session import Session
from werkzeug.utils import secure_filename

# Optional third-party imports
try:
    from dotenv import load_dotenv
except ImportError:
    load_dotenv = None

# Local application imports
from agent_routes import agent_bp
from attendance_processor import AttendanceProcessor
from calendar_processor import CalendarProcessor
from config_manager import config_manager
from data_processor import DataProcessor
from enhanced_file_detector import EnhancedFileDetector
from excel_service import ExcelService
from file_detector import FileTypeDetector
from mcp_client import MCPClient
from openrouter_client import OpenRouterClient
from teamviewer_processor import TeamViewerProcessor
from warning_suppressor import suppress_all_warnings, configure_clean_logging
from wizard_routes import wizard_bp

# Optional local imports
try:
    from api_standardization_system import api_standardization
except ImportError:
    api_standardization = None

# SOPPRESSIONE WARNING - DOPO IMPORT
suppress_all_warnings()
configure_clean_logging()

# Logger configurato dal warning suppressor
logger = logging.getLogger(__name__)

# Carica le variabili d'ambiente dal file .env se presente
if load_dotenv is not None:
    try:
        load_dotenv()
        print("Variabili d'ambiente caricate dal file .env")
    except Exception as e:
        print("Errore nel caricamento delle variabili d'ambiente: %s", str(e))
else:
    print("python-dotenv non installato. Le variabili d'ambiente devono essere impostate manualmente.")

# ===============================================
# MODALITÀ MINIMAL - DISABILITA MONITORAGGIO PRIMA DEGLI IMPORT
# ===============================================
print("🚀 MODALITÀ MINIMAL: Disabilitando sistemi di monitoraggio PRIMA degli import...")
os.environ['DISABLE_PERFORMANCE_MONITORING'] = '1'
os.environ['DISABLE_AUTO_TUNING'] = '1'
os.environ['DISABLE_AGENT_ORCHESTRATOR'] = '1'
os.environ['DISABLE_WORKFLOW_SCHEDULER'] = '1'
os.environ['DISABLE_CACHE_OPTIMIZATION'] = '1'
os.environ['DISABLE_QUERY_OPTIMIZATION'] = '1'
print("✅ MODALITÀ MINIMAL: Tutti i sistemi di monitoraggio disabilitati PRIMA degli import")

# Encoder JSON personalizzato già definito con import organizzati sopra

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        # Mappa dei tipi per ridurre i return statements
        type_handlers = {
            (pd.Timestamp, datetime, date): lambda x: x.isoformat(),
            (pd.Timedelta, timedelta): lambda x: x.total_seconds(),
            np.timedelta64: lambda x: float(x / np.timedelta64(1, 's')),
            np.integer: int,
            np.floating: self._handle_floating,
            np.ndarray: lambda x: x.tolist(),
            float: self._handle_python_float,
            type(None): lambda _: ""
        }

        # Cerca il handler appropriato
        for type_check, handler in type_handlers.items():
            if isinstance(obj, type_check):
                return handler(obj)

        return super().default(obj)

    def _handle_floating(self, obj):
        """Gestisce valori floating numpy."""
        return "" if np.isnan(obj) or np.isinf(obj) else float(obj)

    def _handle_python_float(self, obj):
        """Gestisce valori float Python."""
        return "" if math.isnan(obj) or math.isinf(obj) else obj

# Tutti gli import sono già organizzati nella sezione top-level sopra

# OTTIMIZZAZIONE AVVIO: Caricamento lazy dei moduli pesanti
# Variabili globali per moduli lazy
AI_AGENTS_AVAILABLE = False
ADVANCED_FEATURES_AVAILABLE = False
_ai_agents_loaded = False
_advanced_features_loaded = False
_supabase_systems_loaded = False

def load_ai_agents_lazy():
    """Carica agenti AI solo quando necessario."""
    global AI_AGENTS_AVAILABLE, _ai_agents_loaded
    if _ai_agents_loaded:
        return AI_AGENTS_AVAILABLE

    try:
        # Prova a caricare i moduli AI
        import ai_agents_framework  # pylint: disable=import-outside-toplevel,unused-import
        print("✅ LAZY: ai_agents_framework caricato")

        # Prova caricamento intelligent_automation (opzionale)
        try:
            import intelligent_automation  # pylint: disable=import-outside-toplevel,unused-import
            print("✅ LAZY: intelligent_automation caricato")
        except ImportError:
            print("⚠️ LAZY: intelligent_automation non disponibile (opzionale)")

        AI_AGENTS_AVAILABLE = True
        _ai_agents_loaded = True
        logger.info("Agenti AI caricati con successo (lazy)")
        return True
    except ImportError as e:
        logger.warning("Agenti AI non disponibili: %s", str(e))
        print("⚠️ LAZY: Agenti AI non disponibili - %s", str(e))
        AI_AGENTS_AVAILABLE = False
        _ai_agents_loaded = True
        return False

def load_advanced_features_lazy():
    """Carica funzionalità avanzate solo quando necessario."""
    global ADVANCED_FEATURES_AVAILABLE, _advanced_features_loaded
    if _advanced_features_loaded:
        return ADVANCED_FEATURES_AVAILABLE

    try:
        import advanced_agent_framework  # pylint: disable=import-outside-toplevel,unused-import
        import fase6_integration  # pylint: disable=import-outside-toplevel,unused-import
        import production_monitoring  # pylint: disable=import-outside-toplevel,unused-import
        ADVANCED_FEATURES_AVAILABLE = True
        _advanced_features_loaded = True
        logger.info("Funzionalità avanzate Fase 6-7 caricate con successo (lazy)")
        return True
    except ImportError as e:
        logger.warning("Funzionalità avanzate non disponibili: %s", str(e))
        ADVANCED_FEATURES_AVAILABLE = False
        _advanced_features_loaded = True
        return False

# Configurazione dell'applicazione
app = Flask(__name__)
app.secret_key = 'bait_service_app_secret_key'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['ALLOWED_EXTENSIONS'] = {'csv', 'xlsx', 'xls'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max

# RIMOSSA: Modalità minimal già impostata all'inizio del file

# ===============================================
# REGISTRAZIONE ROUTE CRITICA ANTICIPATA
# ===============================================
print("🚀 REGISTRAZIONE ANTICIPATA: Route /api/config/employees...")

@app.route('/api/config/employees', methods=['GET'])
def get_config_employees_early():
    """
    API per ottenere la lista dei dipendenti configurati.
    REGISTRAZIONE ANTICIPATA per evitare conflitti.
    """
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== API GET_CONFIG_EMPLOYEES ANTICIPATA ===\n")
    sys.stdout.flush()

    try:
        employees = []
        data_source_used = 'unknown'

        # STRATEGIA SEMPLIFICATA: Usa ConfigManager direttamente
        try:
            sys.stdout.write("🔄 ANTICIPATA: Caricamento dipendenti da ConfigManager...\n")
            sys.stdout.flush()

            legacy_employees = config_manager.get_all_employee_costs()

            if legacy_employees:
                # Converti formato legacy in formato standard
                employees = []
                for emp_name, emp_data in legacy_employees.items():
                    employee = {
                        'name': emp_name,
                        'hourly_rate': emp_data.get('hourly_rate', 0),
                        'vat_included': emp_data.get('vat_included', True),
                        'notes': emp_data.get('notes', ''),
                        'source': 'config_manager_early'
                    }
                    employees.append(employee)

                data_source_used = 'config_manager_early'
                sys.stdout.write(f"✅ ANTICIPATA: {len(employees)} dipendenti da ConfigManager\n")
                sys.stdout.flush()

        except Exception as e:
            sys.stdout.write(f"❌ ANTICIPATA: Errore ConfigManager: {str(e)}\n")
            sys.stdout.flush()

        # FALLBACK: Dati demo se nessun dipendente disponibile
        if not employees:
            sys.stdout.write("🎭 ANTICIPATA: Generazione dipendenti demo...\n")
            sys.stdout.flush()

            data_source_used = 'demo_early'
            employees = [
                {
                    'name': 'Mario Rossi',
                    'hourly_rate': 25.0,
                    'vat_included': True,
                    'notes': 'Tecnico senior - Demo Early',
                    'source': 'demo_early'
                },
                {
                    'name': 'Luigi Verdi',
                    'hourly_rate': 20.0,
                    'vat_included': True,
                    'notes': 'Tecnico junior - Demo Early',
                    'source': 'demo_early'
                }
            ]

        # Log finale
        sys.stdout.write(f"👥 Config employees ANTICIPATA - Fonte: {data_source_used}, Dipendenti: {len(employees)}\n")
        sys.stdout.flush()

        return jsonify({
            'success': True,
            'employees': employees,
            'data_source': data_source_used,
            'count': len(employees),
            'timestamp': datetime.now().isoformat(),
            'registration': 'early'
        })

    except Exception as e:
        # Import già organizzati nella sezione top-level
        sys.stdout.write(f"❌ Errore API config/employees ANTICIPATA: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        return jsonify({
            'success': False,
            'error': str(e),
            'employees': [],
            'data_source': 'error_early',
            'registration': 'early'
        }), 500

print("✅ REGISTRAZIONE ANTICIPATA: Route /api/config/employees registrata!")

# OTTIMIZZAZIONE AVVIO: Inizializzazione lazy dei sistemi Supabase
# Variabili globali per sistemi lazy
_supabase_systems_loaded = False

def init_supabase_systems_lazy():
    """Inizializza sistemi Supabase solo quando necessario."""
    global _supabase_systems_loaded
    if _supabase_systems_loaded:
        return hasattr(app, 'db_manager') and app.db_manager is not None

    try:
        print("🔄 LAZY: Inizializzazione sistemi Supabase...")
        from advanced_database_manager import AdvancedDatabaseManager  # pylint: disable=import-outside-toplevel
        from supabase_integration import SupabaseManager  # pylint: disable=import-outside-toplevel
        from automatic_persistence_manager import AutomaticPersistenceManager  # pylint: disable=import-outside-toplevel
        from intelligent_onboarding_system import IntelligentOnboardingSystem  # pylint: disable=import-outside-toplevel

        supabase_manager = SupabaseManager()
        app.db_manager = AdvancedDatabaseManager(supabase_manager)
        app.persistence_manager = AutomaticPersistenceManager(supabase_manager, app.db_manager)
        app.onboarding_system = IntelligentOnboardingSystem(supabase_manager, app.db_manager, app.persistence_manager)

        print("✅ AdvancedDatabaseManager inizializzato - Connesso: %s", app.db_manager.is_connected)
        print("✅ AutomaticPersistenceManager inizializzato")
        print("✅ IntelligentOnboardingSystem inizializzato")

        _supabase_systems_loaded = True
        return True
    except Exception as e:
        print("⚠️ Sistemi avanzati non disponibili: %s", str(e))
        app.db_manager = None
        app.persistence_manager = None
        app.onboarding_system = None
        _supabase_systems_loaded = True
        return False

# Inizializzazione immediata solo per sistemi essenziali
app.db_manager = None
app.persistence_manager = None
app.onboarding_system = None
print("🚀 OTTIMIZZAZIONE: Sistemi Supabase in modalità lazy loading")

# FORZARE INIZIALIZZAZIONE SUPABASE PER GRAFICI INTERATTIVI
print("🔧 FORZARE: Inizializzazione sistemi Supabase per grafici...")
init_supabase_systems_lazy()
print("✅ FORZATO: Database manager inizializzato - Connesso: %s", app.db_manager.is_connected if app.db_manager else False)

# Gestione errori asyncio per Windows - SOLUZIONE DEFINITIVA
# Import già organizzati nella sezione top-level

# Disabilita completamente i warning asyncio su Windows
warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")

# Configura logging per asyncio - SILENZIOSO
asyncio_logger = logging.getLogger('asyncio')
asyncio_logger.setLevel(logging.CRITICAL)  # Solo errori critici
asyncio_logger.disabled = True  # Disabilita completamente

# Handler per errori asyncio non gestiti - AGGRESSIVO
def handle_asyncio_exception(event_loop, context):  # pylint: disable=unused-argument
    exception = context.get('exception')

    # Ignora TUTTI gli errori di connessione comuni su Windows
    if isinstance(exception, (ConnectionResetError, ConnectionAbortedError, OSError, BrokenPipeError)):
        # Silenzioso - non logga nemmeno
        return

    # Ignora errori di callback transport
    if 'transport' in str(context.get('message', '')).lower():
        return

    # Ignora errori di pipe transport
    if '_ProactorBasePipeTransport' in str(context.get('message', '')):
        return

    # Solo per errori veramente critici
    if exception and not isinstance(exception, (ConnectionError, OSError)):
        print("❌ Errore asyncio critico: %s", context)

# Patch asyncio per Windows - SOLUZIONE ROBUSTA
def setup_asyncio_windows_fix():
    """Setup definitivo per risolvere problemi asyncio su Windows"""
    try:
        # Ottieni o crea loop principale
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Imposta handler personalizzato
        loop.set_exception_handler(handle_asyncio_exception)

        # Configura policy per Windows
        if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

        print("✅ AsyncIO Windows fix applicato")

    except Exception as e:
        print("⚠️ Errore setup asyncio fix: %s", e)

# Applica fix
setup_asyncio_windows_fix()

# Aggiungi filtro personalizzato per formattare le date
@app.template_filter('datetime')
def format_datetime(value, date_format='%d/%m/%Y %H:%M'):
    if isinstance(value, (int, float)):
        value = datetime.fromtimestamp(value)
    return value.strftime(date_format)

# Configura Flask per utilizzare l'encoder JSON personalizzato
# Metodo moderno (Flask 2.x) - Import già organizzato nella sezione top-level

class CustomJSONProvider(JSONProvider):
    def dumps(self, obj, **kwargs):
        return json.dumps(obj, cls=CustomJSONEncoder, **kwargs)

    def loads(self, s, **kwargs):
        return json.loads(s, **kwargs)

app.json_provider_class = CustomJSONProvider
app.json = CustomJSONProvider(app)

# CONFIGURAZIONE SESSIONI OTTIMIZZATA per avvio veloce
try:
    # Prova configurazione filesystem (preferita per persistenza)
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_FILE_DIR'] = os.path.join(os.path.dirname(__file__), 'flask_sessions')
    app.config['SESSION_PERMANENT'] = False
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_KEY_PREFIX'] = 'flask_session_'

    # Crea la directory per le sessioni se non esiste
    os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)
    print("✅ Configurazione sessioni filesystem attivata")

except Exception as e:
    # Fallback a sessioni in memoria per evitare problemi I/O
    print("⚠️ Problema configurazione sessioni filesystem: %s", e)
    print("🔄 Fallback a sessioni in memoria...")
    app.config['SESSION_TYPE'] = 'null'  # Sessioni in memoria
    print("✅ Configurazione sessioni in memoria attivata")

# Inizializza il backend di sessione basato su file con gestione errori
try:
    Session(app)
    print("✅ Flask-Session inizializzato correttamente")
except Exception as e:
    print("⚠️ Problema inizializzazione Flask-Session: %s", str(e))
    print("🔄 Continuando senza Flask-Session (sessioni base Flask)...")

# Inizializza i processori di dati
data_processor = DataProcessor()
teamviewer_processor = TeamViewerProcessor()
attendance_processor = AttendanceProcessor()
calendar_processor = CalendarProcessor()

# Inizializza il rilevatore di tipo file
file_detector = FileTypeDetector()
enhanced_file_detector = EnhancedFileDetector()  # Nuovo rilevatore content-based

# Sistema di Standardizzazione API - FASE 4 ALLINEAMENTO FRONTEND-BACKEND
if api_standardization is not None:
    try:
        # Inizializza il sistema di standardizzazione API
        app.api_system = api_standardization
        print("✅ Sistema di Standardizzazione API inizializzato")
    except Exception as e:
        print("⚠️ Sistema di Standardizzazione API non disponibile: %s", str(e))
        app.api_system = None
else:
    print("⚠️ Sistema di Standardizzazione API non disponibile: modulo non importato")
    app.api_system = None

    # Fallback alla funzione originale
    def create_api_response(success=True, data=None, error=None, message=None, status_code=200):
        """
        Crea una risposta API standardizzata - FALLBACK.
        """
        response = {
            'success': success,
            'timestamp': datetime.now().isoformat()
        }

        if data is not None:
            response['data'] = data

        if error:
            response['error'] = error
            response['success'] = False

        if message:
            response['message'] = message

        return response, status_code

    app.api_system = None

def _register_api_endpoints(api_system):
    """
    Registra tutti gli endpoints API nel sistema di standardizzazione.
    """
    try:
        # Endpoints Onboarding
        api_system.register_endpoint(
            path='/api/onboarding/analyze',
            method='POST',
            description='Analisi intelligente dei dati di onboarding',
            tags=['onboarding', 'ai'],
            request_schema={
                'type': 'object',
                'properties': {
                    'files': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'name': {'type': 'string'},
                                'type': {'type': 'string'},
                                'preview': {'type': 'array'},
                                'size': {'type': 'integer'}
                            }
                        }
                    }
                },
                'required': ['files']
            }
        )

        api_system.register_endpoint(
            path='/api/onboarding/progress',
            method='POST',
            description='Salva il progresso dell\'onboarding',
            tags=['onboarding'],
            request_schema={
                'type': 'object',
                'properties': {
                    'user_id': {'type': 'string'},
                    'progress': {'type': 'object'}
                },
                'required': ['progress']
            }
        )

        api_system.register_endpoint(
            path='/api/onboarding/templates',
            method='GET',
            description='Ottiene i template di onboarding disponibili',
            tags=['onboarding']
        )

        # Endpoints Persistenza
        api_system.register_endpoint(
            path='/api/persistence/status',
            method='GET',
            description='Stato del sistema di persistenza automatica',
            tags=['persistence']
        )

        api_system.register_endpoint(
            path='/api/persistence/cleanup',
            method='POST',
            description='Pulizia file vecchi',
            tags=['persistence'],
            request_schema={
                'type': 'object',
                'properties': {
                    'days_old': {'type': 'integer', 'default': 7}
                }
            }
        )

        # Endpoints Dati
        api_system.register_endpoint(
            path='/api/data',
            method='GET',
            description='Ottiene i dati in formato JSON',
            tags=['data']
        )

        api_system.register_endpoint(
            path='/api/processed_data',
            method='GET',
            description='Ottiene i dati elaborati',
            tags=['data']
        )

        api_system.register_endpoint(
            path='/api/chart_data',
            method='GET',
            description='Ottiene i dati per i grafici',
            tags=['data', 'charts']
        )

        # Endpoints Chat AI
        api_system.register_endpoint(
            path='/api/chat/send',
            method='POST',
            description='Invia messaggio alla chat AI',
            tags=['ai', 'chat'],
            request_schema={
                'type': 'object',
                'properties': {
                    'message': {'type': 'string'},
                    'model': {'type': 'string'},
                    'context': {'type': 'object'}
                },
                'required': ['message']
            }
        )

        api_system.register_endpoint(
            path='/api/models',
            method='GET',
            description='Lista dei modelli AI disponibili',
            tags=['ai', 'models']
        )

        # Endpoints Upload
        api_system.register_endpoint(
            path='/upload',
            method='POST',
            description='Upload file dati',
            tags=['upload', 'files']
        )

        # Endpoints Wizard
        api_system.register_endpoint(
            path='/api/wizard/complete',
            method='POST',
            description='Completa il setup wizard',
            tags=['wizard', 'setup']
        )

        # Endpoints Configurazione
        api_system.register_endpoint(
            path='/api/config/employees',
            method='GET',
            description='Lista dipendenti configurati',
            tags=['config', 'employees']
        )

        api_system.register_endpoint(
            path='/api/calculate-employee-cost',
            method='POST',
            description='Calcola il costo di un dipendente',
            tags=['config', 'employees', 'cost']
        )

        # Endpoints Automazione
        api_system.register_endpoint(
            path='/api/automation/rules',
            method='GET',
            description='Lista regole di automazione',
            tags=['automation']
        )

        # Endpoints Sistema Intelligente
        api_system.register_endpoint(
            path='/api/intelligent-system/analyze',
            method='POST',
            description='Analisi incrociata completa',
            tags=['ai', 'analysis']
        )

        # Endpoints Database
        api_system.register_endpoint(
            path='/api/database/status',
            method='GET',
            description='Stato del database',
            tags=['database', 'status']
        )

        # Endpoints Agenti AI - AGGIUNTI
        api_system.register_endpoint(
            path='/api/agents/status',
            method='GET',
            description='Stato di tutti gli agenti AI',
            tags=['agents', 'ai', 'status']
        )

        api_system.register_endpoint(
            path='/api/agents/execute',
            method='POST',
            description='Esegue un task su un agente specifico',
            tags=['agents', 'ai', 'execution'],
            request_schema={
                'type': 'object',
                'properties': {
                    'agent_name': {'type': 'string'},
                    'task_data': {'type': 'object'}
                },
                'required': ['agent_name']
            }
        )

        # Endpoints Automazione - AGGIUNTI
        api_system.register_endpoint(
            path='/api/automation/trigger',
            method='POST',
            description='Attiva manualmente un evento di automazione',
            tags=['automation', 'ai'],
            request_schema={
                'type': 'object',
                'properties': {
                    'event_type': {'type': 'string'},
                    'event_data': {'type': 'object'}
                },
                'required': ['event_type']
            }
        )

        api_system.register_endpoint(
            path='/api/file/analyze_enhanced',
            method='POST',
            description='Analizza un file usando Enhanced Real File Analyzer con agenti AI',
            tags=['file', 'ai', 'analysis']
        )

        # Endpoints Sistema - FASE 4
        api_system.register_endpoint(
            path='/api/endpoints',
            method='GET',
            description='Mappa completa degli endpoints API',
            tags=['system', 'documentation']
        )

        api_system.register_endpoint(
            path='/api/health',
            method='GET',
            description='Controllo stato di salute del sistema',
            tags=['system', 'health']
        )

        print("✅ Registrati %s endpoints API", len(api_system.endpoints_registry))

    except Exception as e:
        print("⚠️ Errore registrazione endpoints: %s", str(e))

# Registra gli endpoints se il sistema di standardizzazione è disponibile
if hasattr(app, 'api_system') and app.api_system:
    _register_api_endpoints(app.api_system)

# Inizializza il client MCP con timeout aumentato e gestione degli errori migliorata
# Usa l'URL del container Docker se disponibile, altrimenti usa localhost con porta 8000 (standard MCP)
mcp_url = os.environ.get('MCP_URL', 'http://127.0.0.1:8000')
mcp_client = MCPClient(base_url=mcp_url, max_retries=2, timeout=5)

# Gestione robusta delle connessioni MCP
def safe_mcp_call(func, *args, **kwargs):
    """Wrapper sicuro per chiamate MCP che gestisce disconnessioni"""
    global mcp_client

    if not mcp_client:
        return None

    try:
        return func(*args, **kwargs)
    except (ConnectionResetError, ConnectionAbortedError, OSError) as e:
        print("⚠️ Connessione MCP persa, tentativo di riconnessione: %s", e)
        try:
            # Tentativo di riconnessione
            if hasattr(mcp_client, 'reconnect'):
                mcp_client.reconnect()
                return func(*args, **kwargs)
            # Se non ha il metodo reconnect, ricrea il client
            mcp_client = MCPClient(base_url=mcp_url, max_retries=2, timeout=5)
            return func(*args, **kwargs)
        except Exception as reconnect_error:
            print("❌ Riconnessione MCP fallita: %s", reconnect_error)
            return None
    except Exception as e:
        print("❌ Errore MCP generico: %s", e)
        return None

# Inizializza il client OpenRouter con gestione errori robusta
# Usa la chiave API dalla variabile d'ambiente se disponibile
openrouter_api_key = os.environ.get('OPENROUTER_API_KEY')
try:
    openrouter_client = OpenRouterClient(api_key=openrouter_api_key, timeout=30)
    logger.info("Client OpenRouter inizializzato")
except Exception as e:
    logger.warning("Errore inizializzazione OpenRouter: %s", str(e))
    # Crea un client mock per evitare errori
    class MockOpenRouterClient:
        def __init__(self):
            self.is_available = False
        def get_models(self, **api_kwargs):  # pylint: disable=unused-argument
            return []
        def chat_completion(self, **chat_kwargs):  # pylint: disable=unused-argument
            return {"error": "OpenRouter non disponibile"}
    openrouter_client = MockOpenRouterClient()
    logger.info("Utilizzando client OpenRouter mock")

# Inizializza il servizio Excel (lazy loading - verrà inizializzato solo quando necessario)
excel_service = None

# Assicurarsi che la cartella uploads esista
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Registra il blueprint degli agenti
try:
    app.register_blueprint(agent_bp)
    print("✅ Blueprint agenti registrato: %s", agent_bp.name)
except Exception as e:
    print("❌ Errore registrazione blueprint agenti: %s", str(e))

# Registra il blueprint del wizard
try:
    app.register_blueprint(wizard_bp)
    print("✅ Blueprint wizard registrato: %s con prefix %s", wizard_bp.name, wizard_bp.url_prefix)

    # Debug: lista le route del wizard
    wizard_routes = []
    for rule in app.url_map.iter_rules():
        if wizard_bp.url_prefix and wizard_bp.url_prefix in str(rule):
            wizard_routes.append(str(rule))

    print("📋 Route wizard registrate: %s", len(wizard_routes))
    for route in wizard_routes:
        print("   - %s", route)

except Exception as e:
    print("❌ Errore registrazione blueprint wizard: %s", str(e))
    # Import già organizzati nella sezione top-level
    traceback.print_exc()

print("🔧 DEBUG: Blueprint registrati, continuando con l'inizializzazione...")

# ===============================================
# ROUTE AGENTI AI E AUTOMAZIONE - REGISTRAZIONE ANTICIPATA
# ===============================================
print("🔧 DEBUG: Registrazione route agenti AI...")

@app.route('/api/agents/status', methods=['GET'])
def get_agents_status():
    """
    Restituisce lo stato di tutti gli agenti AI.
    """
    try:
        # Verifica se gli agenti AI sono disponibili
        if not AI_AGENTS_AVAILABLE:
            return jsonify({
                'success': False,
                'error': 'Agenti AI non disponibili - Moduli ai_agents_framework o intelligent_automation non importati',
                'ai_available': False
            }), 503

        # Importa dinamicamente se necessario
        try:
            from ai_agents_framework import agent_orchestrator  # pylint: disable=import-outside-toplevel
        except ImportError as exc:
            raise ImportError("Modulo ai_agents_framework non disponibile") from exc

        status = agent_orchestrator.get_agents_status()
        return jsonify({
            'success': True,
            'agents_status': status,
            'ai_available': True
        })
    except ImportError as e:
        return jsonify({
            'success': False,
            'error': f'Errore importazione agenti: {str(e)}',
            'ai_available': False
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'ai_available': AI_AGENTS_AVAILABLE
        }), 500

@app.route('/api/agents/execute', methods=['POST'])
def execute_agent_task():
    """
    Esegue un task su un agente specifico.
    """
    if not AI_AGENTS_AVAILABLE:
        return jsonify({
            'success': False,
            'error': 'Agenti AI non disponibili'
        }), 503

    try:
        data = request.get_json()
        agent_name = data.get('agent_name')
        task_data = data.get('task_data', {})

        if not agent_name:
            return jsonify({
                'success': False,
                'error': 'agent_name richiesto'
            }), 400

        # Esegui il task in modo asincrono
        # Import già organizzati nella sezione top-level
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Assicurati che agent_orchestrator sia definito
            if not hasattr(app, 'agent_orchestrator') or app.agent_orchestrator is None:
                # Carica sistemi lazy se non disponibili
                if hasattr(app, 'db_manager') and app.db_manager:
                    from advanced_agent_framework import get_advanced_orchestrator  # pylint: disable=import-outside-toplevel
                    app.agent_orchestrator = get_advanced_orchestrator()  # Nessun parametro richiesto
                else:
                    raise Exception("Database manager non disponibile per inizializzare agent orchestrator")

            task_result = loop.run_until_complete(
                app.agent_orchestrator.execute_task(agent_name, task_data)
            )
        finally:
            loop.close()

        return jsonify({
            'success': True,
            'result': task_result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/automation/rules', methods=['GET'])
def get_automation_rules():
    """
    Restituisce tutte le regole di automazione.
    """
    try:
        # Verifica se il sistema di automazione è disponibile
        if not AI_AGENTS_AVAILABLE:
            return jsonify({
                'success': False,
                'error': 'Sistema di automazione non disponibile - Moduli ai_agents_framework o intelligent_automation non importati',
                'ai_available': False
            }), 503

        # Importa dinamicamente se necessario
        try:
            from intelligent_automation import intelligent_automation  # pylint: disable=import-outside-toplevel
        except ImportError as exc:
            raise ImportError("Modulo intelligent_automation non disponibile") from exc

        rules = intelligent_automation.list_rules()
        stats = intelligent_automation.get_automation_stats()

        return jsonify({
            'success': True,
            'rules': rules,
            'stats': stats,
            'ai_available': True
        })
    except ImportError as e:
        return jsonify({
            'success': False,
            'error': f'Errore importazione automazione: {str(e)}',
            'ai_available': False
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'ai_available': AI_AGENTS_AVAILABLE
        }), 500

print("✅ Route agenti AI registrate anticipatamente")

# REGISTRAZIONE ROUTE DATABASE STATUS
@app.route('/api/database/status', methods=['GET'])
def get_database_status():
    """
    Restituisce lo stato del database e delle connessioni.
    """
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== API DATABASE STATUS ===\n")
    sys.stdout.flush()

    status = {
        'database_available': False,
        'supabase_connected': False,
        'db_manager_active': False,
        'tables_accessible': False,
        'error': None,
        'details': {}
    }

    try:
        # Verifica AdvancedDatabaseManager
        if hasattr(app, 'db_manager') and app.db_manager:
            status['db_manager_active'] = True
            status['supabase_connected'] = app.db_manager.is_connected

            if app.db_manager.is_connected:
                # Test connessione Supabase
                try:
                    test_result = app.db_manager.supabase_manager.test_connection()
                    status['database_available'] = test_result

                    if test_result:
                        # Test accesso tabelle
                        try:
                            normalized_data = app.db_manager.get_normalized_activities()
                            status['tables_accessible'] = True
                            status['details']['normalized_activities'] = len(normalized_data) if normalized_data else 0

                            technicians = app.db_manager.get_master_technicians()
                            status['details']['master_technicians'] = len(technicians) if technicians else 0

                            clients = app.db_manager.get_master_clients()
                            status['details']['master_clients'] = len(clients) if clients else 0

                        except Exception as table_error:
                            status['error'] = f"Errore accesso tabelle: {str(table_error)}"

                except Exception as conn_error:
                    status['error'] = f"Errore test connessione: {str(conn_error)}"
            else:
                status['error'] = "Database manager non connesso"
        else:
            status['error'] = "Database manager non inizializzato"

        # Determina stato generale
        if status['database_available'] and status['tables_accessible']:
            overall_status = "operational"
        elif status['supabase_connected']:
            overall_status = "degraded"
        else:
            overall_status = "offline"

        return jsonify({
            'success': True,
            'status': overall_status,
            'database': status,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        sys.stdout.write(f"❌ Errore database status: {str(e)}\n")
        sys.stdout.flush()

        return jsonify({
            'success': False,
            'status': 'error',
            'error': str(e),
            'database': status,
            'timestamp': datetime.now().isoformat()
        }), 500

print("✅ Route database status registrata")

# REGISTRAZIONE API MODALITÀ MINIMAL
@app.route('/api/minimal-mode/status', methods=['GET'])
def get_minimal_mode_status():
    """
    API per ottenere lo status della modalità minimal.
    """
    try:
        from modalita_minimal_manager import ModalitaMinimalManager  # pylint: disable=import-outside-toplevel

        manager = ModalitaMinimalManager()
        status = manager.get_current_status()

        return jsonify({
            'success': True,
            'data': status,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/minimal-mode/toggle', methods=['POST'])
def toggle_minimal_mode():
    """
    API per attivare/disattivare la modalità minimal.
    """
    try:
        from modalita_minimal_manager import ModalitaMinimalManager  # pylint: disable=import-outside-toplevel

        data = request.get_json() or {}
        enable = data.get('enable')  # True, False, o None per toggle

        manager = ModalitaMinimalManager()
        toggle_result = manager.toggle_minimal_mode(enable)

        return jsonify({
            'success': True,
            'data': toggle_result,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/minimal-mode/restore', methods=['POST'])
def restore_original_code():
    """
    API per ripristinare il codice originale.
    """
    try:
        from modalita_minimal_manager import ModalitaMinimalManager  # pylint: disable=import-outside-toplevel

        manager = ModalitaMinimalManager()
        success = manager.restore_original_code()

        return jsonify({
            'success': success,
            'message': 'Codice originale ripristinato' if success else 'Errore durante il ripristino',
            'restart_required': success,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/modalita-minimal')
def modalita_minimal_page():
    """
    Pagina di gestione della modalità minimal.
    """
    return render_template('modalita_minimal.html')

@app.route('/wizard-interattivo')
def wizard_interattivo_page():
    """
    Pagina del wizard interattivo per caricamento file.
    Implementa TASK 3 del prompt: Wizard di Caricamento File Interattivo.
    """
    return render_template('wizard_interattivo.html')

print("✅ API modalità minimal registrate")

# REGISTRAZIONE API WIZARD INTERATTIVO
@app.route('/api/wizard/interactive/start', methods=['POST'])
def start_interactive_file_analysis():
    """
    API per iniziare l'analisi interattiva di un file.
    Implementa TASK 3 del prompt: Wizard di Caricamento File Interattivo.
    """
    try:
        from interactive_file_wizard import InteractiveFileWizard  # pylint: disable=import-outside-toplevel

        # Verifica che sia stato caricato un file
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'Nessun file fornito',
                'next_step': 'file_upload'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'Nome file vuoto',
                'next_step': 'file_upload'
            }), 400

        # Salva il file temporaneamente
        # Import uuid già presente nella sezione top-level

        filename = secure_filename(file.filename)
        file_id = str(uuid.uuid4())
        temp_dir = os.path.join('uploads', 'temp', file_id)
        os.makedirs(temp_dir, exist_ok=True)

        file_path = os.path.join(temp_dir, filename)
        file.save(file_path)

        # Inizializza wizard interattivo
        wizard = InteractiveFileWizard(
            supabase_manager=getattr(app, 'supabase_manager', None),
            db_manager=getattr(app, 'db_manager', None)
        )

        # Avvia analisi interattiva
        analysis_result = wizard.start_file_analysis(file_path, filename)

        # Salva stato wizard in sessione (SOLO dati serializzabili)
        session[f'wizard_state_{file_id}'] = {
            'file_id': file_id,
            'file_path': file_path,
            'original_filename': filename,
            'current_step': 'user_validation',
            # Salva solo i dati essenziali, NON l'oggetto wizard
            'analysis_summary': {
                'file_type': analysis_result.get('file_type', 'unknown'),
                'total_rows': analysis_result.get('format_analysis', {}).get('total_rows', 0),
                'total_columns': analysis_result.get('format_analysis', {}).get('total_columns', 0)
            }
        }

        # Aggiungi file_id alla risposta
        analysis_result['file_id'] = file_id

        return jsonify({
            'success': True,
            'data': analysis_result,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error("Errore avvio wizard interattivo: %s", str(e))
        return jsonify({
            'success': False,
            'error': str(e),
            'next_step': 'error_handling',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/wizard/interactive/validate', methods=['POST'])
def validate_user_feedback():
    """
    API per processare il feedback utente sulla validazione colonne.
    """
    try:
        import interactive_file_wizard  # pylint: disable=import-outside-toplevel,unused-import

        data = request.get_json()
        file_id = data.get('file_id')
        user_feedback = data.get('feedback', {})

        if not file_id:
            return jsonify({
                'success': False,
                'error': 'file_id richiesto',
                'next_step': 'restart'
            }), 400

        # Recupera stato wizard
        wizard_state_key = f'wizard_state_{file_id}'
        if wizard_state_key not in session:
            return jsonify({
                'success': False,
                'error': 'Stato wizard non trovato',
                'next_step': 'restart'
            }), 404

        wizard_state = session[wizard_state_key]

        # Processa feedback utente
        validation_result = process_user_validation_feedback(
            wizard_state, user_feedback
        )

        # Aggiorna stato
        session[wizard_state_key] = wizard_state

        return jsonify({
            'success': True,
            'data': validation_result,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error("Errore validazione feedback: %s", str(e))
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/wizard/interactive/normalize', methods=['POST'])
def normalize_entities():
    """
    API per la normalizzazione guidata delle entità.
    """
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        entity_mappings = data.get('entity_mappings', {})

        if not file_id:
            return jsonify({
                'success': False,
                'error': 'file_id richiesto'
            }), 400

        # Recupera stato wizard
        wizard_state_key = f'wizard_state_{file_id}'
        if wizard_state_key not in session:
            return jsonify({
                'success': False,
                'error': 'Stato wizard non trovato'
            }), 404

        wizard_state = session[wizard_state_key]

        # Processa normalizzazione entità
        normalization_result = process_entity_normalization(
            wizard_state, entity_mappings
        )

        # Aggiorna stato
        session[wizard_state_key] = wizard_state

        return jsonify({
            'success': True,
            'data': normalization_result,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error("Errore normalizzazione entità: %s", str(e))
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/wizard/interactive/insert', methods=['POST'])
def insert_normalized_data():
    """
    API per l'inserimento finale dei dati normalizzati in Supabase.
    """
    try:
        data = request.get_json()
        file_id = data.get('file_id')
        confirm_insert = data.get('confirm_insert', False)

        if not file_id:
            return jsonify({
                'success': False,
                'error': 'file_id richiesto'
            }), 400

        if not confirm_insert:
            return jsonify({
                'success': False,
                'error': 'Conferma inserimento richiesta'
            }), 400

        # Recupera stato wizard
        wizard_state_key = f'wizard_state_{file_id}'
        if wizard_state_key not in session:
            return jsonify({
                'success': False,
                'error': 'Stato wizard non trovato'
            }), 404

        wizard_state = session[wizard_state_key]

        # Inserisci dati in Supabase
        insertion_result = insert_data_to_supabase(wizard_state)

        # Pulisci stato se inserimento riuscito
        if insertion_result.get('success'):
            session.pop(wizard_state_key, None)

            # Pulisci file temporaneo
            try:
                # Import shutil già presente nella sezione top-level
                temp_dir = os.path.dirname(wizard_state['file_path'])
                shutil.rmtree(temp_dir, ignore_errors=True)
            except Exception:  # Specifica il tipo di eccezione
                pass

        return jsonify({
            'success': True,
            'data': insertion_result,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error("Errore inserimento dati: %s", str(e))
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# FUNZIONI DI SUPPORTO PER WIZARD INTERATTIVO

def process_user_validation_feedback(wizard_state, user_feedback):
    """
    Processa il feedback utente per la validazione delle colonne.

    Args:
        wizard_state: Stato corrente del wizard
        user_feedback: Feedback fornito dall'utente

    Returns:
        Dict con risultati della validazione
    """
    try:
        sys.stdout.write("🔍 Processando feedback utente...\n")
        sys.stdout.flush()

        # Estrai informazioni dal feedback
        format_correct = user_feedback.get('format_correct', True)
        columns_correct = user_feedback.get('columns_correct', True)
        modifications = user_feedback.get('modifications', {})

        validation_result = {
            'validation_accepted': format_correct and columns_correct,
            'modifications_applied': [],
            'next_step': 'entity_normalization' if format_correct and columns_correct else 'format_correction'
        }

        # Se ci sono modifiche da applicare
        if modifications:
            validation_result['modifications_applied'] = apply_user_modifications(wizard_state, modifications)

        # Se tutto è validato, procedi alla normalizzazione entità
        if validation_result['validation_accepted']:
            entity_analysis = analyze_entities_for_normalization(wizard_state)
            validation_result['entity_analysis'] = entity_analysis

        sys.stdout.write(f"✅ Feedback processato: {validation_result['next_step']}\n")
        sys.stdout.flush()

        return validation_result

    except Exception as e:
        logger.error("Errore processing feedback: %s", str(e))
        return {
            'validation_accepted': False,
            'error': str(e),
            'next_step': 'error_handling'
        }

def apply_user_modifications(wizard_state, modifications):
    """
    Applica le modifiche richieste dall'utente.

    Args:
        wizard_state: Stato del wizard
        modifications: Modifiche da applicare

    Returns:
        Lista delle modifiche applicate
    """
    applied_modifications = []

    try:
        # Modifica delimitatore CSV
        if 'delimiter' in modifications:
            new_delimiter = modifications['delimiter']
            # Rileggi file con nuovo delimitatore
            file_path = wizard_state['file_path']
            df = pd.read_csv(file_path, delimiter=new_delimiter)
            # Salva il dataframe aggiornato nel file system invece che in sessione
            applied_modifications.append(f"Delimitatore cambiato in '{new_delimiter}'")

        # Modifica riga header
        if 'header_row' in modifications:
            header_row = int(modifications['header_row'])
            file_path = wizard_state['file_path']
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, header=header_row)
            else:
                df = pd.read_excel(file_path, header=header_row)
            # Salva modifiche nel file system invece che in sessione
            applied_modifications.append(f"Header impostato alla riga {header_row}")

        # Rinomina colonne
        if 'column_renames' in modifications:
            renames = modifications['column_renames']
            # Ricarica il dataframe dal file
            file_path = wizard_state['file_path']
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            df.rename(columns=renames, inplace=True)
            applied_modifications.append(f"Rinominate {len(renames)} colonne")

        # Modifica tipi colonne
        if 'column_types' in modifications:
            types = modifications['column_types']
            # Salva le modifiche ai tipi in una struttura separata
            if 'column_type_modifications' not in wizard_state:
                wizard_state['column_type_modifications'] = {}
            wizard_state['column_type_modifications'].update(types)
            applied_modifications.append(f"Modificati tipi di {len(types)} colonne")

        return applied_modifications

    except Exception as e:
        logger.error("Errore applicazione modifiche: %s", str(e))
        return [f"Errore: {str(e)}"]

def analyze_entities_for_normalization(wizard_state):
    """
    Analizza le entità nel file per la normalizzazione.

    Args:
        wizard_state: Stato del wizard

    Returns:
        Dict con analisi delle entità
    """
    try:
        # Ricarica il dataframe dal file invece di usare wizard_instance
        file_path = wizard_state['file_path']
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)

        # Usa le colonne rilevate dall'analisi iniziale
        detected_columns = wizard_state.get('analysis_result', {}).get('feedback_data', {}).get('detected_columns', {})

        entities_found = {
            'employees': [],
            'clients': [],
            'projects': [],
            'vehicles': []
        }

        # Cerca colonne con entità
        for col_name, col_info in detected_columns.items():
            col_type = col_info['detected_type']

            if col_type == 'employee_name':
                unique_employees = df[col_name].dropna().unique().tolist()
                entities_found['employees'] = unique_employees[:20]  # Limita a 20

            elif col_type == 'client_name':
                unique_clients = df[col_name].dropna().unique().tolist()
                entities_found['clients'] = unique_clients[:20]

            elif col_type == 'project_name':
                unique_projects = df[col_name].dropna().unique().tolist()
                entities_found['projects'] = unique_projects[:20]

            elif col_type == 'vehicle_plate':
                unique_vehicles = df[col_name].dropna().unique().tolist()
                entities_found['vehicles'] = unique_vehicles[:20]

        # Verifica esistenza in Supabase
        supabase_entities = check_entities_in_supabase(entities_found, wizard_state)

        return {
            'entities_found': entities_found,
            'supabase_entities': supabase_entities,
            'normalization_required': any(len(entities) > 0 for entities in entities_found.values()),
            'total_entities': sum(len(entities) for entities in entities_found.values())
        }

    except Exception as e:
        logger.error("Errore analisi entità: %s", str(e))
        return {
            'entities_found': {},
            'error': str(e),
            'normalization_required': False
        }

def check_entities_in_supabase(entities_found, wizard_state=None):
    """
    Verifica quali entità esistono già in Supabase.

    Args:
        entities_found: Entità trovate nel file
        wizard_state: Stato del wizard (opzionale)

    Returns:
        Dict con status entità in Supabase
    """
    # Usa wizard_state se fornito, altrimenti ignora
    _ = wizard_state  # Evita warning variabile non utilizzata
    supabase_status = {
        'employees': {'existing': [], 'new': []},
        'clients': {'existing': [], 'new': []},
        'projects': {'existing': [], 'new': []},
        'vehicles': {'existing': [], 'new': []}
    }

    try:
        # Ottieni manager database se disponibile
        db_manager = getattr(app, 'db_manager', None)

        if not db_manager:
            # Se non c'è db_manager, considera tutto come nuovo
            for entity_type, entities in entities_found.items():
                supabase_status[entity_type]['new'] = entities
            return supabase_status

        # Verifica dipendenti
        if entities_found['employees']:
            existing_employees = db_manager.get_master_technicians()
            existing_names = [emp.get('normalized_name', '') for emp in existing_employees]

            for emp in entities_found['employees']:
                if emp.lower().strip() in [name.lower() for name in existing_names]:
                    supabase_status['employees']['existing'].append(emp)
                else:
                    supabase_status['employees']['new'].append(emp)

        # Verifica clienti
        if entities_found['clients']:
            existing_clients = db_manager.get_master_clients()
            existing_names = [client.get('normalized_name', '') for client in existing_clients]

            for client in entities_found['clients']:
                if client.lower().strip() in [name.lower() for name in existing_names]:
                    supabase_status['clients']['existing'].append(client)
                else:
                    supabase_status['clients']['new'].append(client)

        # Verifica progetti
        if entities_found['projects']:
            existing_projects = db_manager.get_master_projects()
            existing_names = [proj.get('project_name', '') for proj in existing_projects]

            for project in entities_found['projects']:
                if project.lower().strip() in [name.lower() for name in existing_names]:
                    supabase_status['projects']['existing'].append(project)
                else:
                    supabase_status['projects']['new'].append(project)

        # Verifica veicoli
        if entities_found['vehicles']:
            existing_vehicles = db_manager.get_master_vehicles()
            existing_plates = [veh.get('license_plate', '') for veh in existing_vehicles]

            for vehicle in entities_found['vehicles']:
                if vehicle.upper().strip() in [plate.upper() for plate in existing_plates]:
                    supabase_status['vehicles']['existing'].append(vehicle)
                else:
                    supabase_status['vehicles']['new'].append(vehicle)

        return supabase_status

    except Exception as e:
        logger.error("Errore verifica entità Supabase: %s", str(e))
        # In caso di errore, considera tutto come nuovo
        for entity_type, entities in entities_found.items():
            supabase_status[entity_type]['new'] = entities
        return supabase_status

def process_entity_normalization(wizard_state, entity_mappings):
    """
    Processa la normalizzazione delle entità con le mappature utente.

    Args:
        wizard_state: Stato del wizard
        entity_mappings: Mappature fornite dall'utente

    Returns:
        Dict con risultati normalizzazione
    """
    try:
        sys.stdout.write("🔄 Processando normalizzazione entità...\n")
        sys.stdout.flush()

        normalization_results = {
            'entities_created': [],
            'entities_mapped': [],
            'normalization_complete': True,
            'next_step': 'data_insertion'
        }

        # Processa ogni tipo di entità
        for entity_type, mappings in entity_mappings.items():
            if entity_type in ['employees', 'clients', 'projects', 'vehicles']:
                entity_result = process_single_entity_type(entity_type, mappings, wizard_state)
                normalization_results['entities_created'].extend(entity_result.get('created', []))
                normalization_results['entities_mapped'].extend(entity_result.get('mapped', []))

        # Salva mappature normalizzate nello stato
        wizard_state['normalized_entities'] = entity_mappings

        sys.stdout.write("✅ Normalizzazione completata\n")
        sys.stdout.flush()

        return normalization_results

    except Exception as e:
        logger.error("Errore normalizzazione entità: %s", str(e))
        return {
            'normalization_complete': False,
            'error': str(e),
            'next_step': 'error_handling'
        }

def process_single_entity_type(entity_type, mappings, wizard_state=None):
    """
    Processa la normalizzazione di un singolo tipo di entità.

    Args:
        entity_type: Tipo di entità (employees, clients, etc.)
        mappings: Mappature per questo tipo
        wizard_state: Stato del wizard (opzionale)

    Returns:
        Dict con risultati per questo tipo
    """
    # Usa wizard_state se fornito, altrimenti ignora
    _ = wizard_state  # Evita warning variabile non utilizzata
    entity_processing_result = {'created': [], 'mapped': []}

    try:
        db_manager = getattr(app, 'db_manager', None)
        if not db_manager:
            return entity_processing_result

        for entity_name, mapping_info in mappings.items():
            action = mapping_info.get('action', 'create')

            if action == 'create':
                # Crea nuova entità
                new_id = None
                if entity_type == 'employees':
                    new_id = db_manager.create_or_get_technician(entity_name)
                    entity_processing_result['created'].append(f"Dipendente: {entity_name} (ID: {new_id})")
                elif entity_type == 'clients':
                    new_id = db_manager.create_or_get_client(entity_name)
                    entity_processing_result['created'].append(f"Cliente: {entity_name} (ID: {new_id})")
                elif entity_type == 'projects':
                    new_id = db_manager.create_or_get_project(entity_name, mapping_info.get('client_id'))
                    entity_processing_result['created'].append(f"Progetto: {entity_name} (ID: {new_id})")
                elif entity_type == 'vehicles':
                    new_id = db_manager.create_or_get_vehicle(entity_name)
                    entity_processing_result['created'].append(f"Veicolo: {entity_name} (ID: {new_id})")

                # Log dell'ID creato
                if new_id:
                    logger.info("Entità %s creata con ID: %s", entity_type, new_id)

            elif action == 'map':
                # Mappa a entità esistente
                existing_id = mapping_info.get('existing_id')
                entity_processing_result['mapped'].append(f"{entity_name} -> ID {existing_id}")

        return entity_processing_result

    except Exception as e:
        logger.error("Errore processing %s: %s", entity_type, str(e))
        return entity_processing_result

def insert_data_to_supabase(wizard_state):
    """
    Inserisce i dati normalizzati in Supabase.

    Args:
        wizard_state: Stato del wizard con dati normalizzati

    Returns:
        Dict con risultati inserimento
    """
    try:
        sys.stdout.write("💾 Inserimento dati in Supabase...\n")
        sys.stdout.flush()

        df = wizard_state['wizard_instance'].current_dataframe
        detected_columns = wizard_state['wizard_instance'].detected_columns
        normalized_entities = wizard_state.get('normalized_entities', {})

        # Determina tipo di file per tabella destinazione
        file_type = determine_file_type_for_insertion(detected_columns)

        # Prepara dati per inserimento
        prepared_data = prepare_data_for_insertion(df, detected_columns, normalized_entities, file_type)

        # Inserisci in Supabase
        insertion_result = perform_supabase_insertion(prepared_data, file_type, wizard_state)

        sys.stdout.write(f"✅ Inserimento completato: {insertion_result.get('records_inserted', 0)} record\n")
        sys.stdout.flush()

        return insertion_result

    except Exception as e:
        logger.error("Errore inserimento Supabase: %s", str(e))
        return {
            'success': False,
            'error': str(e),
            'records_inserted': 0
        }

def determine_file_type_for_insertion(detected_columns):
    """
    Determina il tipo di file basato sulle colonne rilevate.

    Args:
        detected_columns: Colonne rilevate

    Returns:
        Tipo di file per inserimento
    """
    column_types = [col['detected_type'] for col in detected_columns.values()]

    if 'vehicle_plate' in column_types:
        return 'vehicle_usage'
    if 'employee_name' in column_types and 'client_name' in column_types:
        return 'activities'
    if 'employee_name' in column_types and 'date' in column_types:
        return 'timesheets'
    return 'generic'

def prepare_data_for_insertion(df, detected_columns, normalized_entities, file_type=None):
    """
    Prepara i dati per l'inserimento in Supabase.

    Args:
        df: DataFrame con i dati
        detected_columns: Colonne rilevate
        normalized_entities: Entità normalizzate
        file_type: Tipo di file (opzionale)

    Returns:
        Lista di record preparati
    """
    # Usa file_type se fornito per ottimizzazioni specifiche
    _ = file_type  # Evita warning variabile non utilizzata
    prepared_records = []

    for _, row in df.iterrows():
        record = {}

        # Mappa ogni colonna al campo corrispondente
        for col_name, col_info in detected_columns.items():
            col_type = col_info['detected_type']
            value = row[col_name]

            if pd.isna(value):
                continue

            # Normalizza valore basato sul tipo
            if col_type == 'employee_name':
                record['technician_id'] = get_normalized_entity_id('employees', str(value), normalized_entities)
            elif col_type == 'client_name':
                record['client_id'] = get_normalized_entity_id('clients', str(value), normalized_entities)
            elif col_type == 'project_name':
                record['project_id'] = get_normalized_entity_id('projects', str(value), normalized_entities)
            elif col_type == 'vehicle_plate':
                record['vehicle_id'] = get_normalized_entity_id('vehicles', str(value), normalized_entities)
            elif col_type == 'date':
                record['date'] = pd.to_datetime(value).date().isoformat()
            elif col_type in ['time_start', 'time_end']:
                record[col_type] = str(value)
            else:
                record[col_name.lower()] = str(value)

        if record:  # Solo se il record ha dati
            prepared_records.append(record)

    return prepared_records

def get_normalized_entity_id(entity_type, entity_name, normalized_entities):
    """
    Ottiene l'ID normalizzato per un'entità.

    Args:
        entity_type: Tipo di entità
        entity_name: Nome dell'entità
        normalized_entities: Mappature normalizzate

    Returns:
        ID dell'entità o None
    """
    try:
        entity_mappings = normalized_entities.get(entity_type, {})
        entity_mapping = entity_mappings.get(entity_name, {})

        if entity_mapping.get('action') == 'map':
            return entity_mapping.get('existing_id')
        if entity_mapping.get('action') == 'create':
            return entity_mapping.get('created_id')

        return None

    except Exception as e:
        logger.error("Errore get entity ID: %s", str(e))
        return None

def perform_supabase_insertion(prepared_data, file_type, wizard_state=None):
    """
    Esegue l'inserimento effettivo in Supabase.

    Args:
        prepared_data: Dati preparati
        file_type: Tipo di file
        wizard_state: Stato del wizard (opzionale)

    Returns:
        Dict con risultati inserimento
    """
    # Usa wizard_state se fornito per logging aggiuntivo
    _ = wizard_state  # Evita warning variabile non utilizzata
    try:
        db_manager = getattr(app, 'db_manager', None)
        if not db_manager:
            return {
                'success': False,
                'error': 'Database manager non disponibile',
                'records_inserted': 0
            }

        # Determina tabella destinazione
        table_mapping = {
            'activities': 'normalized_activities',
            'vehicle_usage': 'normalized_vehicle_usage',
            'timesheets': 'normalized_timesheets',
            'generic': 'processed_data'
        }

        target_table = table_mapping.get(file_type, 'processed_data')

        # Inserisci record in batch
        records_inserted = 0
        batch_size = 100

        for i in range(0, len(prepared_data), batch_size):
            batch = prepared_data[i:i + batch_size]

            try:
                insert_result = db_manager.client.table(target_table).insert(batch).execute()
                records_inserted += len(batch)

                # Log del risultato dell'inserimento
                if insert_result and hasattr(insert_result, 'data'):
                    sys.stdout.write(f"   ✅ Batch {i//batch_size + 1}: {len(batch)} record inseriti (Supabase response: {len(insert_result.data) if insert_result.data else 0})\n")
                else:
                    sys.stdout.write(f"   ✅ Batch {i//batch_size + 1}: {len(batch)} record inseriti\n")
                sys.stdout.flush()

            except Exception as batch_error:
                sys.stdout.write(f"   ❌ Errore batch {i//batch_size + 1}: {str(batch_error)}\n")
                sys.stdout.flush()

        # TASK 6: Esegui controlli di coerenza post-inserimento
        coherence_results = None
        if records_inserted > 0:
            try:
                sys.stdout.write("🔍 Esecuzione controlli di coerenza post-inserimento...\n")
                sys.stdout.flush()

                from coherence_checker import CoherenceChecker  # pylint: disable=import-outside-toplevel
                checker = CoherenceChecker(db_manager)
                coherence_results = checker.run_all_coherence_checks()

                sys.stdout.write(f"✅ Controlli completati. Score: {coherence_results.get('overall_coherence_score', 0):.1f}%\n")
                sys.stdout.flush()

            except Exception as coherence_error:
                sys.stdout.write(f"⚠️ Errore controlli coerenza: {str(coherence_error)}\n")
                sys.stdout.flush()

        return {
            'success': True,
            'records_inserted': records_inserted,
            'target_table': target_table,
            'total_records': len(prepared_data),
            'coherence_check': coherence_results
        }

    except Exception as e:
        logger.error("Errore inserimento Supabase: %s", str(e))
        return {
            'success': False,
            'error': str(e),
            'records_inserted': 0
        }

print("✅ API wizard interattivo registrate")

# CARICAMENTO FORZATO AGENTI AI ALL'AVVIO
print("🤖 CARICAMENTO FORZATO: Agenti AI...")
try:
    load_ai_agents_lazy()
    if AI_AGENTS_AVAILABLE:
        print("✅ FORZATO: Agenti AI caricati con successo")
    else:
        print("⚠️ FORZATO: Agenti AI non disponibili ma app continua")
except Exception as e:
    print("❌ FORZATO: Errore caricamento agenti AI: %s", e)
    print("⚠️ FORZATO: App continua senza agenti AI")

# OTTIMIZZAZIONE AVVIO: Funzionalità avanzate in modalità lazy loading
print("🚀 OTTIMIZZAZIONE: Funzionalità avanzate in modalità lazy loading")

def init_advanced_features_lazy():
    """Inizializza funzionalità avanzate solo quando necessario."""
    if not load_advanced_features_lazy():
        return False

    try:
        # Inizializza integrazione Fase 6 (agenti avanzati)
        from fase6_integration import init_fase6_integration  # pylint: disable=import-outside-toplevel
        from production_monitoring import init_production_monitoring  # pylint: disable=import-outside-toplevel
        from advanced_agent_framework import get_advanced_orchestrator  # pylint: disable=import-outside-toplevel

        fase6_integration = init_fase6_integration(app)
        logger.info("Integrazione Fase 6 inizializzata con successo (lazy): %s", type(fase6_integration).__name__)

        # Inizializza monitoring produzione
        production_monitoring = init_production_monitoring(app)
        logger.info("Monitoring produzione inizializzato con successo (lazy): %s", type(production_monitoring).__name__)

        # Ottieni orchestratore agenti avanzato
        advanced_orchestrator = get_advanced_orchestrator()
        logger.info("Orchestratore agenti avanzato inizializzato con %d agenti (lazy)", len(advanced_orchestrator.agents))

        return True
    except Exception as e:
        logger.error("Errore inizializzazione funzionalità avanzate: %s", str(e))
        return False

# Funzione per verificare l'estensione del file
def allowed_file(filename):
    if not filename or '.' not in filename:
        return False
    ext = filename.rsplit('.', 1)[1].lower()
    return ext in app.config['ALLOWED_EXTENSIONS']

# Funzione per leggere file CSV con separatore italiano (;)
def read_csv_file(file_path):
    try:
        df = pd.read_csv(file_path, sep=';', encoding='utf-8-sig')
        return df, None
    except Exception as e:
        return None, str(e)

# Funzione per leggere file Excel
def read_excel_file(file_path):
    global excel_service

    try:
        # Prima prova con pandas
        try:
            df = pd.read_excel(file_path, engine='openpyxl')
            return df, None
        except Exception as pandas_error:
            # Se fallisce, prova con il servizio Excel COM
            try:
                # Inizializza il servizio Excel se non è già stato fatto
                if excel_service is None:
                    excel_service = ExcelService(visible=False)

                # Verifica se il servizio Excel è disponibile
                if not excel_service.is_available():
                    return None, f"Servizio Excel COM non disponibile: {str(pandas_error)}"

                # Apri il workbook
                workbook = excel_service.open_workbook(file_path)
                if workbook is None:
                    return None, "Impossibile aprire il file Excel con COM"

                # Leggi il foglio di lavoro
                df = excel_service.read_worksheet(workbook)

                # Chiudi il workbook
                excel_service.close_workbook(workbook)

                if df is None:
                    return None, "Impossibile leggere il foglio di lavoro con COM"

                return df, None
            except Exception as com_error:
                return None, f"Errore nella lettura del file Excel: {str(pandas_error)} / COM: {str(com_error)}"
    except Exception as e:
        return None, str(e)

# Funzione per leggere qualsiasi file supportato con gestione robusta
def read_file(file_path, file_type_hint=None):
    """
    Legge un file con gestione robusta degli errori.

    Args:
        file_path: Percorso del file
        file_type_hint: Suggerimento sul tipo di file per usare parser specifici (opzionale)

    Returns:
        Tuple (DataFrame, error_message)
    """
    # Usa file_type_hint se fornito per ottimizzazioni
    _ = file_type_hint  # Evita warning variabile non utilizzata

    # Gestione case-insensitive delle estensioni
    file_path_lower = file_path.lower()

    if file_path_lower.endswith('.csv'):
        # Usa sempre il parser robusto per i file CSV
        try:
            from robust_csv_parser import RobustCSVParser  # pylint: disable=import-outside-toplevel
            parser = RobustCSVParser()
            df, error = parser.parse_csv_file(file_path)

            if error:
                # Fallback al parser standard se il robusto fallisce
                logger.warning("Parser robusto fallito: %s, provo parser standard", error)
                return read_csv_file(file_path)

            return df, None

        except Exception as e:
            logger.error("Errore nel parser robusto: %s, provo parser standard", e)
            # Fallback al parser standard
            return read_csv_file(file_path)

    elif file_path_lower.endswith(('.xlsx', '.xls')):
        return read_excel_file(file_path)
    else:
        return None, "Formato file non supportato"

# Funzione per rilevare il tipo di file solo dal nome (per parsing preliminare)
def detect_file_type_from_name(filename):
    """
    Rileva il tipo di file basandosi solo sul nome del file.
    Usato per scegliere il parser appropriato prima di leggere il contenuto.
    """
    filename_lower = filename.lower()

    # Rilevamento basato su parole chiave nel nome del file
    # Mappa delle parole chiave per tipo di file
    file_type_keywords = {
        'calendario': ['calendar', 'calendario', 'cal_', 'eventi'],
        'teamviewer': ['teamviewer', 'tv_', 'remote'],
        'timbrature': ['timbrature', 'presenze', 'attendance'],
        'permessi': ['permessi', 'ferie', 'leave'],
        'attivita': ['attivita', 'activity', 'lavoro'],
        'registro_auto': ['registro', 'auto', 'vehicle'],
        'progetti': ['progetti', 'project']
    }

    # Cerca corrispondenze
    for file_type, keywords in file_type_keywords.items():
        if any(keyword in filename_lower for keyword in keywords):
            return file_type

    # File temporanei che spesso sono calendari (pattern comune)
    if filename_lower.startswith('tmp-') and filename_lower.endswith('.csv'):
        return 'calendario'  # Assume che i file tmp- siano calendari

    return 'generico'

# Funzione per determinare il tipo di file in base al nome e al contenuto
def detect_file_type(filename, df):  # pylint: disable=too-many-branches,too-many-statements
    """
    Determina il tipo di file in base al nome e al contenuto.
    Utilizza sia l'analisi del nome del file che la classe FileTypeDetector per un rilevamento più accurato.

    Args:
        filename: Nome del file
        df: DataFrame pandas con i dati del file

    Returns:
        Tipo di file rilevato (stringa)
    """
    # Import già organizzati nella sezione top-level

    # Verifica che il filename non sia None o vuoto
    if not filename:
        sys.stdout.write("ERRORE: Nome file vuoto o None\n")
        sys.stdout.flush()
        return 'generico'

    # Verifica che il DataFrame non sia None o vuoto
    if df is None or df.empty:
        sys.stdout.write("ERRORE: DataFrame vuoto o None\n")
        sys.stdout.flush()
        return 'generico'

    # Converti il nome del file in minuscolo per confronti case-insensitive
    filename_lower = filename.lower()
    file_extension = os.path.splitext(filename_lower)[1]

    # Stampa informazioni di debug avanzate
    sys.stdout.write("=== RILEVAMENTO TIPO FILE ===\n")
    sys.stdout.write(f"Nome file: {filename_lower}\n")
    sys.stdout.write(f"Estensione: {file_extension}\n")
    sys.stdout.write(f"Colonne disponibili: {df.columns.tolist()}\n")
    sys.stdout.flush()

    # RILEVAMENTO BASATO SUL CONTENUTO (CONTENT-BASED)
    # Il sistema ora analizza SOLO il contenuto del file, non il nome
    sys.stdout.write("Utilizzo FileTypeDetector per analizzare le colonne (content-based detection)...\n")

    # Utilizza l'Enhanced File Detector per rilevamento content-based avanzato
    detected_type, confidence_score, type_scores = enhanced_file_detector.detect_file_type(df, filename)

    # Fallback al rilevatore originale se l'enhanced non trova nulla
    if detected_type == "unknown" or confidence_score < 0.3:
        sys.stdout.write("Enhanced detector non ha trovato match sicuri, provo con il detector originale...\n")
        fallback_type, fallback_score, fallback_scores = file_detector.detect_file_type(df)
        if fallback_score > confidence_score:
            detected_type = fallback_type
            confidence_score = fallback_score
            type_scores = fallback_scores
            sys.stdout.write(f"Usato fallback detector: {detected_type} (score: {fallback_score:.3f})\n")

    # Stampa i risultati del rilevamento
    sys.stdout.write("Risultati del rilevamento basato sulle colonne:\n")
    sys.stdout.write(f"- Tipo rilevato: {detected_type}\n")
    sys.stdout.write(f"- Confidenza: {confidence_score:.2f}\n")
    sys.stdout.write("Punteggi per tipo:\n")
    for file_type, score in type_scores.items():
        sys.stdout.write(f"- {file_type}: {score:.2f}\n")

    # Se il tipo rilevato è "unknown", utilizza "generico"
    if detected_type == "unknown":
        detected_type = "generico"
        sys.stdout.write("Tipo non riconosciuto, utilizzo 'generico'\n")

    # Controlli aggiuntivi basati sull'estensione del file
    if file_extension in ['.xlsx', '.xls'] and detected_type == 'calendario':
        sys.stdout.write("Correzione: File Excel non può essere calendario\n")
        # Cerca il secondo miglior tipo
        second_best = sorted([(t, s) for t, s in type_scores.items() if t != 'calendario'],
                            key=lambda x: x[1], reverse=True)
        if second_best and second_best[0][1] > 0.3:  # Se c'è un'alternativa con punteggio decente
            detected_type = second_best[0][0]
            sys.stdout.write(f"Tipo corretto a: {detected_type}\n")
        else:
            detected_type = 'generico'
            sys.stdout.write("Nessuna alternativa valida, utilizzo 'generico'\n")

    sys.stdout.write(f"Tipo di file finale: {detected_type}\n")
    sys.stdout.write("=== FINE RILEVAMENTO ===\n")
    sys.stdout.flush()

    return detected_type

# Route per pulire la sessione
@app.route('/clear_session')
def clear_session_route():
    # Salva la lista dei file recenti prima di pulire la sessione
    recent_files = session.get('recent_files', [])

    # Pulisci la sessione
    session.clear()

    # Ripristina la lista dei file recenti
    session['recent_files'] = recent_files

    flash('Sessione pulita con successo', 'success')
    return redirect(url_for('index'))

# Route per caricare un file recente
@app.route('/load_recent_file/<int:file_index>')
def load_recent_file(file_index):
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== CARICAMENTO FILE RECENTE ===\n")
    sys.stdout.flush()

    # Verifica che ci siano file recenti
    recent_files = session.get('recent_files', [])
    if not recent_files or file_index >= len(recent_files):
        flash('File recente non trovato', 'error')
        return redirect(url_for('index'))

    # Ottieni le informazioni sul file recente
    file_info = recent_files[file_index]
    file_path = file_info.get('file_path')

    # Verifica che il file esista ancora
    if not os.path.exists(file_path):
        flash('Il file non esiste più sul server', 'error')
        # Rimuovi il file dalla lista dei file recenti
        recent_files.pop(file_index)
        session['recent_files'] = recent_files
        return redirect(url_for('index'))

    # Pulisci le chiavi specifiche relative ai dati precedenti
    keys_to_clear = [
        'preview_data', 'columns', 'filename', 'original_filename',
        'file_path', 'file_type', 'stats', 'upload_timestamp',
        'session_id', 'mcp_file_id', 'processed_data_path'
    ]
    for key in keys_to_clear:
        if key in session:
            session.pop(key)

    # Carica i dati dal file
    try:
        # Leggi il file
        df, error = read_file(file_path)

        if error:
            flash(f'Errore nella lettura del file: {error}', 'error')
            return redirect(url_for('index'))

        # Prepara i dati di anteprima
        preview_data = df.head(10).to_dict(orient='records')
        columns = df.columns.tolist()

        # Salva in sessione
        session['preview_data'] = preview_data
        session['columns'] = columns
        session['filename'] = file_info.get('unique_filename')
        session['original_filename'] = file_info.get('filename')
        session['file_path'] = file_path
        session['file_type'] = file_info.get('file_type')
        session['stats'] = {}
        session['upload_timestamp'] = int(time.time())
        session['session_id'] = file_info.get('session_id')
        session['mcp_file_id'] = file_info.get('mcp_file_id')

        # Sposta questo file all'inizio della lista dei file recenti
        recent_files.pop(file_index)
        recent_files.insert(0, file_info)
        session['recent_files'] = recent_files

        flash(f'File caricato con successo: {file_info.get("filename")}', 'success')
        return redirect(url_for('preview'))

    except Exception as e:
        # Import già organizzati nella sezione top-level
        sys.stdout.write(f"Errore nel caricamento del file recente: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()
        flash(f'Errore nel caricamento del file: {str(e)}', 'error')
        return redirect(url_for('index'))

# Route principale
@app.route('/')
def index():
    # Aggiungi un parametro per forzare il refresh della pagina e evitare problemi di cache
    timestamp = int(time.time())

    # Ottieni la lista dei file recenti
    recent_files = session.get('recent_files', [])

    # Verifica che i file esistano ancora
    valid_recent_files = []
    for file_info in recent_files:
        file_path = file_info.get('file_path')
        if os.path.exists(file_path):
            valid_recent_files.append(file_info)

    # Aggiorna la lista dei file recenti
    if len(valid_recent_files) != len(recent_files):
        session['recent_files'] = valid_recent_files
        recent_files = valid_recent_files

    return render_template('index.html', timestamp=timestamp, recent_files=recent_files)

# Route per l'upload dei file - DOPPIA REGISTRAZIONE per compatibilità
@app.route('/upload', methods=['POST'])
@app.route('/upload_file', methods=['POST'])
def upload_file():  # pylint: disable=too-many-branches,too-many-statements,too-many-nested-blocks
    # Import uuid già presente nella sezione top-level

    # Controlla se è in modalità wizard
    wizard_mode = request.form.get('wizard_mode') == 'true'

    sys.stdout.write(f"=== INIZIO CARICAMENTO FILE (Wizard: {wizard_mode}) ===\n")
    sys.stdout.flush()

    # Pulisci solo le chiavi specifiche relative ai dati precedenti, non l'intera sessione
    # Questo permette di mantenere altre informazioni come l'utente loggato, le preferenze, ecc.
    keys_to_clear = [
        'preview_data', 'columns', 'filename', 'original_filename',
        'file_path', 'file_type', 'stats', 'upload_timestamp',
        'session_id', 'mcp_file_id', 'processed_data_path'
    ]
    for key in keys_to_clear:
        if key in session:
            session.pop(key)

    # Pulisci la cache dei grafici
    global chart_cache
    chart_cache = {}

    # Debug: stampa tutte le informazioni sulla richiesta
    sys.stdout.write(f"Request method: {request.method}\n")
    sys.stdout.write(f"Request form: {request.form}\n")
    sys.stdout.write(f"Request files: {request.files}\n")
    sys.stdout.flush()

    try:
        # Verifica se ci sono file nella richiesta
        if 'file' not in request.files:
            sys.stdout.write("Errore: Nessun file nei request.files\n")
            sys.stdout.flush()

            if wizard_mode:
                return jsonify({
                    'success': False,
                    'error': 'Nessun file selezionato'
                }), 400

            flash('Nessun file selezionato', 'error')
            return redirect(url_for('index'))

        file = request.files['file']
        sys.stdout.write(f"File ricevuto: {file.filename}\n")
        sys.stdout.flush()

        # Verifica se il nome del file è vuoto
        if file.filename == '':
            sys.stdout.write("Errore: Nome file vuoto\n")
            sys.stdout.flush()

            if wizard_mode:
                return jsonify({
                    'success': False,
                    'error': 'Nessun file selezionato'
                }), 400

            flash('Nessun file selezionato', 'error')
            return redirect(url_for('index'))

        # Verifica se il file è vuoto
        file_content = file.read()
        if len(file_content) == 0:
            sys.stdout.write("Errore: File vuoto\n")
            sys.stdout.flush()
            flash('Il file selezionato è vuoto', 'error')
            return redirect(url_for('index'))

        # Riposiziona il puntatore del file all'inizio
        file.seek(0)

        # Verifica se il file è di un formato supportato
        if not allowed_file(file.filename):
            sys.stdout.write(f"Formato file non supportato: {file.filename}\n")
            sys.stdout.flush()
            flash('Formato file non supportato', 'error')
            return redirect(url_for('index'))

        # Salva il file
        try:
            # Assicurati che la cartella uploads esista
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

            filename = secure_filename(file.filename)
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            sys.stdout.write(f"Percorso file: {file_path}\n")
            sys.stdout.flush()

            # Genera un nome file univoco con timestamp e UUID per evitare conflitti
            # Import uuid già presente nella sezione top-level
            timestamp = int(time.time())
            random_id = uuid.uuid4().hex[:8]  # Aggiungi un ID casuale per maggiore unicità
            base_name, ext = os.path.splitext(filename)
            unique_filename = f"{base_name}_{timestamp}_{random_id}{ext}"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

            sys.stdout.write(f"Nome file univoco generato: {unique_filename}\n")
            sys.stdout.flush()

            # Usa il sistema di persistenza automatica se disponibile
            if app.persistence_manager:
                sys.stdout.write("🔄 Usando sistema persistenza automatica\n")
                sys.stdout.flush()

                # Prepara dati di sessione
                session_data = {
                    'session_id': session.get('session_id', str(uuid.uuid4())),
                    'user_agent': request.headers.get('User-Agent', ''),
                    'ip_address': request.remote_addr
                }

                # Salva con persistenza automatica
                persistence_result = app.persistence_manager.save_uploaded_file_with_persistence(
                    file, session_data
                )

                if persistence_result['success']:
                    # Usa i dati del sistema di persistenza
                    file_path = persistence_result['file_path']
                    unique_filename = persistence_result['filename']

                    sys.stdout.write(f"✅ File salvato con persistenza automatica: {unique_filename}\n")
                    if persistence_result.get('supabase_id'):
                        sys.stdout.write(f"✅ Persistito in Supabase con ID: {persistence_result['supabase_id']}\n")
                    sys.stdout.flush()
                else:
                    raise Exception(f"Errore persistenza automatica: {persistence_result.get('error')}")
            else:
                # Fallback al sistema tradizionale
                sys.stdout.write("⚠️ Sistema persistenza non disponibile, uso metodo tradizionale\n")
                sys.stdout.flush()

                # Salva il file con il nome univoco
                file.save(file_path)
                sys.stdout.write(f"✅ File salvato (metodo tradizionale): {unique_filename}\n")
                sys.stdout.flush()

            # Verifica che il file sia stato salvato correttamente
            if not os.path.exists(file_path):
                sys.stdout.write(f"ERRORE: Il file non è stato salvato correttamente: {file_path}\n")
                sys.stdout.flush()
                flash('Errore nel salvare il file', 'error')
                return redirect(url_for('index'))

            # Verifica che il file esista dopo il salvataggio
            if not os.path.exists(file_path):
                sys.stdout.write(f"Errore: Il file non esiste dopo il salvataggio: {file_path}\n")
                sys.stdout.flush()
                flash('Errore nel salvare il file: il file non esiste dopo il salvataggio', 'error')
                return redirect(url_for('index'))

            # Verifica che il file non sia vuoto
            if os.path.getsize(file_path) == 0:
                sys.stdout.write(f"Errore: Il file è vuoto: {file_path}\n")
                sys.stdout.flush()
                flash('Errore nel salvare il file: il file è vuoto', 'error')
                return redirect(url_for('index'))

        except Exception as e:
            sys.stdout.write(f"Errore nel salvare il file: {str(e)}\n")
            sys.stdout.write(traceback.format_exc())
            sys.stdout.flush()
            flash(f'Errore nel salvare il file: {str(e)}', 'error')
            return redirect(url_for('index'))

        # Leggi il file e reindirizza alla pagina di anteprima
        try:
            # Prima rileva il tipo di file dal nome per usare il parser appropriato
            preliminary_file_type = detect_file_type_from_name(unique_filename)
            sys.stdout.write(f"Tipo file preliminare rilevato: {preliminary_file_type}\n")
            sys.stdout.flush()

            # Leggi il file usando il parser appropriato
            df, error = read_file(file_path, file_type_hint=preliminary_file_type)
            if error:
                sys.stdout.write(f"Errore nella lettura del file: {error}\n")
                sys.stdout.flush()
                flash(f'Errore nella lettura del file: {error}', 'error')
                return redirect(url_for('index'))

            # Verifica che il DataFrame non sia vuoto
            if df.empty:
                sys.stdout.write("Errore: DataFrame vuoto\n")
                sys.stdout.flush()
                flash('Il file non contiene dati validi', 'error')
                return redirect(url_for('index'))

            sys.stdout.write(f"File letto con successo, colonne: {df.columns.tolist()}\n")
            sys.stdout.flush()

            # Prepara i dati per l'anteprima - limita a 20 righe per ridurre la dimensione della sessione
            # Converti le date in stringhe per evitare problemi di serializzazione JSON
            df_copy = df.copy()

            # Converti tutte le colonne di tipo datetime in stringhe
            for col in df_copy.select_dtypes(include=['datetime64']).columns:
                df_copy[col] = df_copy[col].astype(str)

            # Converti in dizionario
            preview_data = df_copy.head(20).to_dict('records')
            columns = df.columns.tolist()

            # Stampa la dimensione dei dati di anteprima
            # Import json già presente nella sezione top-level

            # Usa l'encoder personalizzato definito a livello globale
            preview_json = json.dumps(preview_data, cls=CustomJSONEncoder)
            preview_size = len(preview_json)
            sys.stdout.write(f"Dimensione dati di anteprima: {preview_size} bytes\n")
            sys.stdout.flush()

            # Rileva il tipo di file
            file_type = detect_file_type(unique_filename, df)  # Usa il nome univoco per il rilevamento
            sys.stdout.write(f"Tipo di file rilevato: {file_type}\n")
            sys.stdout.flush()

            # Debug avanzato per il rilevamento del tipo di file
            sys.stdout.write("DEBUG RILEVAMENTO TIPO FILE:\n")
            sys.stdout.write(f"- Nome file originale: {filename}\n")
            sys.stdout.write(f"- Nome file univoco: {unique_filename}\n")
            sys.stdout.write(f"- Estensione: {os.path.splitext(unique_filename)[1]}\n")
            sys.stdout.write(f"- Colonne: {df.columns.tolist()}\n")
            sys.stdout.write(f"- Tipo rilevato: {file_type}\n")
            sys.stdout.flush()

            # Verifica se il file è già stato elaborato (controllo per evitare problemi di cache)
            if 'filename' in session and session.get('filename') == filename:
                sys.stdout.write(f"File già elaborato: {filename}, pulizia dati precedenti\n")
                sys.stdout.flush()
                # Pulisci solo le chiavi specifiche relative ai dati precedenti
                keys_to_clear = [
                    'preview_data', 'columns', 'filename', 'original_filename',
                    'file_path', 'file_type', 'stats', 'upload_timestamp',
                    'session_id', 'mcp_file_id', 'processed_data_path'
                ]
                for key in keys_to_clear:
                    if key in session:
                        session.pop(key)

            # Genera un ID univoco per questa sessione
            session_id = f"{int(time.time())}_{hash(filename)}"
            sys.stdout.write(f"ID sessione generato: {session_id}\n")
            sys.stdout.flush()

            # Invia il file al server MCP per l'elaborazione
            try:
                # Prepara i dati per l'elaborazione MCP
                mcp_file_id = session_id  # Usa lo stesso ID sessione come ID file per MCP

                sys.stdout.write("Invio file al server MCP per l'elaborazione...\n")
                sys.stdout.write(f"- ID file MCP: {mcp_file_id}\n")
                sys.stdout.write(f"- Tipo file: {file_type}\n")
                sys.stdout.flush()

                # Elabora il file con MCP
                mcp_result = mcp_client.process_file(
                    file_id=mcp_file_id,
                    file_path=file_path,
                    file_type=file_type
                )

                sys.stdout.write(f"Risultato elaborazione MCP: {mcp_result}\n")
                sys.stdout.flush()

                # Verifica se ci sono errori
                if 'error' in mcp_result:
                    sys.stdout.write(f"Errore nell'elaborazione MCP: {mcp_result['error']}\n")
                    sys.stdout.flush()

                    # Verifica se dobbiamo usare l'elaborazione locale
                    if mcp_result.get('use_local', True):
                        sys.stdout.write("Utilizzo dell'elaborazione locale come fallback\n")
                        sys.stdout.flush()
                        # Continua con l'elaborazione locale
                    else:
                        # Se l'errore è critico e non possiamo usare l'elaborazione locale
                        flash(f"Errore nell'elaborazione del file: {mcp_result['error']}", 'error')
                        return redirect(url_for('index'))
                else:
                    # Aggiorna il tipo di file se MCP ha rilevato un tipo diverso
                    if 'file_type' in mcp_result and mcp_result['file_type'] != file_type:
                        sys.stdout.write(f"MCP ha rilevato un tipo di file diverso: {mcp_result['file_type']} (locale: {file_type})\n")
                        sys.stdout.flush()
                        file_type = mcp_result['file_type']

                    # Ottieni statistiche aggiuntive da MCP
                    if 'statistics' in mcp_result:
                        sys.stdout.write(f"Statistiche MCP: {mcp_result['statistics']}\n")
                        sys.stdout.flush()
            except Exception as e:
                sys.stdout.write(f"Errore nella comunicazione con MCP: {str(e)}\n")
                sys.stdout.flush()
                # Continua comunque con l'elaborazione locale

            # Salva in sessione (senza pulire l'intera sessione)
            session['preview_data'] = preview_data
            session['columns'] = columns
            session['filename'] = unique_filename  # Usa il nome univoco del file
            session['original_filename'] = filename  # Salva anche il nome originale
            session['file_path'] = file_path
            session['file_type'] = file_type
            session['stats'] = {}
            session['upload_timestamp'] = int(time.time())  # Aggiungi timestamp per evitare problemi di cache
            session['session_id'] = session_id
            session['mcp_file_id'] = mcp_file_id  # Salva l'ID file MCP

            # Salva anche nella lista dei file recenti
            recent_files = session.get('recent_files', [])
            # Aggiungi il file corrente all'inizio della lista
            current_file_info = {
                'filename': filename,
                'unique_filename': unique_filename,
                'file_path': file_path,
                'file_type': file_type,
                'timestamp': int(time.time()),
                'session_id': session_id,
                'mcp_file_id': mcp_file_id
            }
            # Rimuovi eventuali duplicati (stesso nome file)
            recent_files = [f for f in recent_files if f.get('filename') != filename]
            # Aggiungi il nuovo file all'inizio
            recent_files.insert(0, current_file_info)
            # Limita la lista a 5 file recenti
            recent_files = recent_files[:5]
            session['recent_files'] = recent_files

            sys.stdout.write("Dati salvati in sessione con successo\n")
            sys.stdout.write(f"Nome file originale: {filename}\n")
            sys.stdout.write(f"Nome file univoco: {unique_filename}\n")
            sys.stdout.write(f"Tipo file rilevato: {file_type}\n")
            sys.stdout.flush()

            # Se è in modalità wizard, restituisci JSON
            if wizard_mode:
                # Pulisci i dati di preview per evitare errori JSON
                clean_preview = []
                if preview_data:
                    for row in preview_data[:10]:  # Prime 10 righe
                        clean_row = {}
                        for key, value in row.items():
                            # Gestisci valori problematici per JSON
                            if value is None:
                                clean_row[key] = ""
                            elif isinstance(value, float):
                                if str(value).lower() in ['nan', 'inf', '-inf']:
                                    clean_row[key] = ""
                                else:
                                    clean_row[key] = value
                            else:
                                clean_row[key] = str(value) if value is not None else ""
                        clean_preview.append(clean_row)

                return jsonify({
                    'success': True,
                    'message': 'File caricato con successo',
                    'filename': filename,
                    'file_type': file_type,
                    'preview': clean_preview
                })

            return redirect(url_for('preview'))

        except Exception as e:
            sys.stdout.write(f"Errore nell'elaborazione del file: {str(e)}\n")
            sys.stdout.write(traceback.format_exc())
            sys.stdout.flush()

            if wizard_mode:
                return jsonify({
                    'success': False,
                    'error': f'Errore nell\'elaborazione del file: {str(e)}'
                }), 500
            flash(f'Errore nell\'elaborazione del file: {str(e)}', 'error')
            return redirect(url_for('index'))

    except Exception as e:
        sys.stdout.write(f"Errore generale nella funzione upload_file: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        if wizard_mode:
            return jsonify({
                'success': False,
                'error': f'Errore durante il caricamento del file: {str(e)}'
            }), 500
        flash(f'Errore durante il caricamento del file: {str(e)}', 'error')
        return redirect(url_for('index'))

# Route per l'anteprima dei dati
@app.route('/preview')
def preview():
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== ACCESSO ALLA PAGINA DI ANTEPRIMA ===\n")
    sys.stdout.flush()

    # Verifica che ci siano dati in sessione
    if 'preview_data' not in session:
        sys.stdout.write("Nessun dato in sessione\n")
        sys.stdout.flush()
        flash('Nessun dato da visualizzare. Carica prima un file.', 'error')
        return redirect(url_for('index'))

    # Verifica che il file esista
    if 'file_path' in session:
        file_path = session.get('file_path')
        if not file_path:
            sys.stdout.write("ERRORE: Percorso file vuoto o None\n")
            sys.stdout.flush()
            flash('Percorso file non valido. Carica nuovamente il file.', 'error')
            session.clear()
            return redirect(url_for('index'))

        try:
            if not os.path.exists(file_path):
                sys.stdout.write(f"ERRORE: Il file non esiste: {file_path}\n")
                sys.stdout.flush()
                flash('Il file non esiste più. Carica nuovamente il file.', 'error')
                session.clear()
                return redirect(url_for('index'))
        except Exception as e:
            sys.stdout.write(f"ERRORE nella verifica dell'esistenza del file: {str(e)}\n")
            sys.stdout.flush()
            flash(f'Errore nella verifica del file: {str(e)}. Carica nuovamente il file.', 'error')
            session.clear()
            return redirect(url_for('index'))

    preview_data = session['preview_data']
    columns = session['columns']
    filename = session.get('original_filename', session['filename'])  # Usa il nome originale se disponibile
    file_type = session.get('file_type', 'generico')
    stats = session.get('stats', {})

    # Stampa informazioni dettagliate sul file
    sys.stdout.write(f"Nome file originale: {session.get('original_filename', 'non disponibile')}\n")
    sys.stdout.write(f"Nome file univoco: {session.get('filename', 'non disponibile')}\n")

    # Stampa informazioni di debug
    sys.stdout.write(f"Anteprima per file: {filename}, tipo: {file_type}\n")
    sys.stdout.write(f"Timestamp caricamento: {session.get('upload_timestamp', 'non disponibile')}\n")
    sys.stdout.write(f"ID sessione: {session.get('session_id', 'non disponibile')}\n")
    sys.stdout.flush()

    return render_template('preview.html',
                          preview_data=preview_data,
                          columns=columns,
                          filename=filename,
                          file_type=file_type,
                          stats=stats)

# Route per l'elaborazione dei dati
@app.route('/process', methods=['POST'])
def process_data():
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== ELABORAZIONE DATI ===\n")
    sys.stdout.flush()

    # Verifica che ci sia un file in sessione
    if 'file_path' not in session:
        sys.stdout.write("Nessun file in sessione\n")
        sys.stdout.flush()
        flash('Nessun file da elaborare. Carica prima un file.', 'error')
        return redirect(url_for('index'))

    file_path = session.get('file_path')
    if not file_path:
        sys.stdout.write("ERRORE: Percorso file vuoto o None\n")
        sys.stdout.flush()
        flash('Percorso file non valido. Carica nuovamente il file.', 'error')
        session.clear()
        return redirect(url_for('index'))

    file_type = session.get('file_type', 'generico')

    sys.stdout.write(f"Percorso file: {file_path}\n")
    sys.stdout.flush()

    # Verifica che il file esista
    try:
        file_path = session.get('file_path')
        if not file_path or not os.path.exists(file_path):
            sys.stdout.write(f"ERRORE: Il file non esiste: {file_path}\n")
            sys.stdout.flush()
            flash('Il file non esiste più. Carica nuovamente il file.', 'error')
            session.clear()
            return redirect(url_for('index'))
    except Exception as e:
        sys.stdout.write(f"ERRORE nella verifica dell'esistenza del file: {str(e)}\n")
        sys.stdout.flush()
        flash(f'Errore nella verifica del file: {str(e)}. Carica nuovamente il file.', 'error')
        session.clear()
        return redirect(url_for('index'))

    # Stampa informazioni dettagliate sul file
    sys.stdout.write(f"Elaborazione - Nome file originale: {session.get('original_filename', 'non disponibile')}\n")
    sys.stdout.write(f"Elaborazione - Nome file univoco: {session.get('filename', 'non disponibile')}\n")
    sys.stdout.write(f"Elaborazione - Tipo file: {file_type}\n")
    sys.stdout.flush()

    try:
        # Inizializza stats con valori di default per evitare possibly-used-before-assignment
        stats = {
            'total_activities': 0,
            'total_hours': 0,
            'average_duration': 0,
            'technicians_count': 0,
            'clients_count': 0,
            'source': 'local'
        }

        # Inizializza processed_df con None per evitare possibly-used-before-assignment
        processed_df = None

        # Verifica se abbiamo un ID file MCP
        mcp_file_id = session.get('mcp_file_id')

        # Se abbiamo un ID file MCP, prova a ottenere il riepilogo dal server MCP
        if mcp_file_id:
            sys.stdout.write(f"Tentativo di ottenere il riepilogo dal server MCP per il file ID: {mcp_file_id}\n")
            sys.stdout.flush()

            try:
                # Ottieni il riepilogo dal server MCP
                mcp_summary = mcp_client.get_file_summary(mcp_file_id)

                sys.stdout.write(f"Riepilogo MCP: {mcp_summary}\n")
                sys.stdout.flush()

                # Verifica se ci sono errori
                if 'error' not in mcp_summary:
                    # Usa le statistiche dal server MCP
                    stats = {
                        'total_activities': mcp_summary.get('total_activities', 0),
                        'total_hours': mcp_summary.get('total_hours', 0),
                        'average_duration': mcp_summary.get('average_duration', 0),
                        'technicians_count': mcp_summary.get('technicians_count', 0),
                        'clients_count': mcp_summary.get('clients_count', 0),
                        'source': 'mcp'  # Indica che le statistiche provengono dal server MCP
                    }

                    sys.stdout.write("Statistiche ottenute dal server MCP\n")
                    sys.stdout.flush()

                    # Carica il file per l'elaborazione locale
                    df, error = read_file(file_path, file_type_hint=file_type)
                    if error:
                        flash(f'Errore nella lettura del file: {error}', 'error')
                        return redirect(url_for('index'))

                    # Usa l'elaborazione locale solo per ottenere il DataFrame
                    processed_df = df.copy()

                    # Continua con il flusso normale
                else:
                    sys.stdout.write(f"Errore nel riepilogo MCP: {mcp_summary.get('error')}\n")
                    sys.stdout.flush()

                    # Verifica se dobbiamo usare l'elaborazione locale
                    if mcp_summary.get('use_local', True):
                        sys.stdout.write("Utilizzo dell'elaborazione locale come fallback\n")
                        sys.stdout.flush()
                        # Continua con l'elaborazione locale
                    else:
                        # Se l'errore è critico e non possiamo usare l'elaborazione locale
                        flash(f"Errore nell'elaborazione del file: {mcp_summary.get('error')}", 'error')
                        return redirect(url_for('index'))
            except Exception as e:
                sys.stdout.write(f"Errore nella comunicazione con MCP: {str(e)}\n")
                sys.stdout.flush()
                # Continua con l'elaborazione locale

        # Se non abbiamo un ID file MCP o c'è stato un errore, usa l'elaborazione locale
        if processed_df is None:
            sys.stdout.write(f"Utilizzo dell'elaborazione locale per il tipo di file: {file_type}\n")
            sys.stdout.flush()

            # Elabora il file in base al tipo
            if 'teamviewer' in file_type:
                processed_df = teamviewer_processor.process_teamviewer_file(file_path)
                stats = teamviewer_processor.generate_summary_stats(processed_df)
            elif file_type == 'calendario':
                processed_df = calendar_processor.process_calendar_file(file_path)
                stats = calendar_processor.generate_summary_stats(processed_df)
            elif file_type == 'timbrature':
                # File di timbrature - usa attendance_processor che gestisce ore_lavorate
                processed_df = attendance_processor.process_attendance_file(file_path)
                stats = attendance_processor.generate_summary_stats(processed_df)
            elif file_type == 'permessi':
                # File di permessi - usa elaborazione generica (NON attendance_processor)
                sys.stdout.write("Elaborazione file di permessi con processore generico\n")
                sys.stdout.flush()
                df, error = read_file(file_path, file_type_hint=file_type)
                if error:
                    flash(f'Errore nella lettura del file: {error}', 'error')
                    return redirect(url_for('index'))
                processed_df = data_processor.process_dataframe(df)
                stats = {}
            elif file_type == 'attivita':
                # Per i file di attività, utilizziamo il processore di calendario che è il più adatto
                # in quanto gestisce date, orari e durate in modo simile
                sys.stdout.write("Elaborazione file di attività con il processore di calendario\n")
                sys.stdout.flush()
                processed_df = calendar_processor.process_calendar_file(file_path)
                stats = calendar_processor.generate_summary_stats(processed_df)
            else:
                # Elaborazione generica
                sys.stdout.write(f"Elaborazione generica per il tipo di file: {file_type}\n")
                sys.stdout.flush()
                df, error = read_file(file_path, file_type_hint=file_type)
                if error:
                    flash(f'Errore nella lettura del file: {error}', 'error')
                    return redirect(url_for('index'))
                processed_df = data_processor.process_dataframe(df)
                stats = {}

        # Verifica finale che processed_df sia definito
        if processed_df is None:
            sys.stdout.write("⚠️ ERRORE: processed_df non definito, creazione DataFrame vuoto\n")
            sys.stdout.flush()
            processed_df = pd.DataFrame()

        # Salva i risultati in un file temporaneo invece che nella sessione
        # Import già organizzati nella sezione top-level

        # Crea un ID univoco per il file temporaneo
        temp_id = f"{int(time.time())}_{uuid.uuid4().hex}"

        # Crea una directory temporanea se non esiste
        temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp')
        os.makedirs(temp_dir, exist_ok=True)

        # Salva i dati elaborati con persistenza automatica
        temp_data_path = os.path.join(temp_dir, f"processed_data_{temp_id}.json")

        # Crea una copia del DataFrame per la conversione
        processed_df_copy = processed_df.copy()

        # Converti tutte le colonne di tipo datetime in stringhe
        for col in processed_df_copy.select_dtypes(include=['datetime64']).columns:
            processed_df_copy[col] = processed_df_copy[col].astype(str)

        # Limita a 500 righe per ridurre la dimensione del file
        limited_data = processed_df_copy.head(500).to_dict('records')

        # Salva i dati con l'encoder personalizzato
        with open(temp_data_path, 'w', encoding='utf-8') as f:
            json.dump(limited_data, f, cls=CustomJSONEncoder)

        # Usa il sistema di persistenza automatica se disponibile
        if app.persistence_manager:
            sys.stdout.write("🔄 Persistenza automatica dati elaborati\n")
            sys.stdout.flush()

            try:
                # Salva dati elaborati con persistenza automatica
                persistence_result = app.persistence_manager.save_processed_data_with_persistence(
                    file_id=session.get('filename', temp_id),
                    processed_data=limited_data,
                    statistics=stats
                )

                if persistence_result['success']:
                    sys.stdout.write("✅ Dati elaborati persistiti automaticamente\n")
                    if persistence_result.get('supabase_id'):
                        sys.stdout.write(f"✅ Persistiti in Supabase con ID: {persistence_result['supabase_id']}\n")
                    sys.stdout.flush()
                else:
                    sys.stdout.write(f"⚠️ Errore persistenza dati elaborati: {persistence_result.get('error')}\n")
                    sys.stdout.flush()
            except Exception as e:
                sys.stdout.write(f"⚠️ Errore persistenza automatica dati elaborati: {str(e)}\n")
                sys.stdout.flush()

        # Salva solo il percorso del file temporaneo nella sessione
        session['processed_data_path'] = temp_data_path
        session['stats'] = stats

        # Stampa informazioni sul file temporaneo
        sys.stdout.write(f"Dati elaborati salvati in file temporaneo: {temp_data_path}\n")
        sys.stdout.write(f"Dimensione file: {os.path.getsize(temp_data_path)} bytes\n")
        sys.stdout.flush()

        flash('Dati elaborati con successo!', 'success')
        return redirect(url_for('dashboard'))

    except Exception as e:
        flash(f'Errore nell\'elaborazione dei dati: {str(e)}', 'error')
        return redirect(url_for('preview'))

# Route per la dashboard - MODERNIZZATA SUPABASE-FIRST
@app.route('/dashboard')
def dashboard():
    """
    Dashboard Standard modernizzata con approccio Supabase-first.
    Prioritizza dati da Supabase, fallback intelligente a sessione.
    """
    try:
        import sys

        sys.stdout.write("=== DASHBOARD STANDARD SUPABASE-FIRST ===\n")
        sys.stdout.flush()

        # Inizializza variabili
        data = []
        stats = {}
        filename = 'Dashboard'
        file_type = 'dashboard'
        data_source = 'none'

        # PRIORITÀ 1: Dati da Supabase (approccio moderno)
        try:
            from advanced_database_manager import AdvancedDatabaseManager
            from supabase_integration import SupabaseManager

            supabase_manager = SupabaseManager()
            if supabase_manager.is_connected:
                db_manager = AdvancedDatabaseManager(supabase_manager)

                # Ottieni dati normalizzati da Supabase
                supabase_data = db_manager.get_normalized_activities(limit=1000)

                if supabase_data and len(supabase_data) > 0:
                    data = supabase_data
                    data_source = 'supabase'
                    filename = 'Dati Supabase'
                    file_type = 'normalized'

                    # Calcola statistiche moderne
                    stats = calculate_modern_stats(data, file_type)

                    sys.stdout.write(f"✅ Dati caricati da Supabase: {len(data)} record\n")
                    sys.stdout.flush()
                else:
                    sys.stdout.write("⚠️ Nessun dato normalizzato in Supabase\n")
                    sys.stdout.flush()
            else:
                sys.stdout.write("⚠️ Supabase non connesso\n")
                sys.stdout.flush()

        except Exception as supabase_error:
            sys.stdout.write(f"⚠️ Errore Supabase: {str(supabase_error)}\n")
            sys.stdout.flush()

        # PRIORITÀ 2: Dati da sessione (fallback intelligente)
        if not data:
            # Verifica wizard completato
            if session.get('wizard_completed'):
                sys.stdout.write("✅ Wizard completato - dashboard vuota consentita\n")
                sys.stdout.flush()
                return render_template('dashboard.html',
                                      data=None,
                                      stats={},
                                      filename='Setup Wizard Completato',
                                      file_type='wizard',
                                      data_source='wizard')

            # Verifica dati in sessione
            if 'processed_data' in session or 'preview_data' in session:
                # Usa dati elaborati se disponibili
                if 'processed_data_path' in session:
                    processed_data_path = session['processed_data_path']
                    try:
                        if os.path.exists(processed_data_path):
                            with open(processed_data_path, 'r', encoding='utf-8') as f:
                                # Import json già presente nella sezione top-level
                                data = json.load(f)
                            data_source = 'processed_file'
                            sys.stdout.write(f"✅ Dati da file elaborato: {len(data)} record\n")
                        else:
                            raise FileNotFoundError("File elaborato non trovato")
                    except Exception as e:
                        sys.stdout.write(f"⚠️ Errore file elaborato: {str(e)}\n")
                        data = session.get('preview_data', [])
                        data_source = 'session_preview'
                else:
                    data = session.get('preview_data', [])
                    data_source = 'session_preview'

                # Ottieni metadati da sessione
                filename = session.get('original_filename', session.get('filename', 'File Caricato'))
                file_type = session.get('file_type', 'generico')
                stats = session.get('stats', {})

                # Ricalcola statistiche se necessario
                if not stats and data:
                    stats = calculate_modern_stats(data, file_type)

                sys.stdout.write(f"✅ Dati da sessione: {len(data)} record\n")
                sys.stdout.flush()
            else:
                # Nessun dato disponibile
                sys.stdout.write("❌ Nessun dato disponibile\n")
                sys.stdout.flush()
                flash('Nessun dato da visualizzare. Carica un file o completa il setup wizard.', 'info')
                return redirect(url_for('index'))

        # Log finale
        sys.stdout.write(f"📊 Dashboard caricata - Fonte: {data_source}, Record: {len(data)}, Tipo: {file_type}\n")
        sys.stdout.flush()

        return render_template('dashboard.html',
                              data=data,
                              stats=stats,
                              filename=filename,
                              file_type=file_type,
                              data_source=data_source)

    except Exception as e:
        import traceback
        sys.stdout.write(f"❌ Errore Dashboard: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        flash(f'Errore nel caricamento della dashboard: {str(e)}', 'error')
        return redirect(url_for('index'))

# Route per la visualizzazione dei dati grezzi - RIMOSSA
# Sezione legacy rimossa durante rifinitura app - backup in legacy-backup/

# Route per la visualizzazione dei grafici interattivi
@app.route('/interactive-charts')
def interactive_charts():
    import sys

    sys.stdout.write("=== ACCESSO AI GRAFICI INTERATTIVI ===\n")
    sys.stdout.flush()

    # STRATEGIA SUPABASE-FIRST: Priorità assoluta a Supabase
    data = []
    data_source_used = 'unknown'

    # STEP 1: Prova a caricare da Supabase
    if hasattr(app, 'db_manager') and app.db_manager:
        try:
            sys.stdout.write("🚀 SUPABASE: Caricamento dati da AdvancedDatabaseManager...\n")
            sys.stdout.flush()

            # Ottieni dati elaborati da Supabase
            supabase_data = app.db_manager.get_processed_data()

            if supabase_data and len(supabase_data) > 0:
                data = supabase_data
                data_source_used = 'supabase'

                sys.stdout.write(f"✅ SUPABASE: {len(data)} record caricati con successo (fonte: {data_source_used})\n")
                sys.stdout.flush()
            else:
                sys.stdout.write("⚠️ SUPABASE: Nessun dato trovato\n")
                sys.stdout.flush()

        except Exception as e:
            sys.stdout.write(f"❌ SUPABASE: Errore caricamento dati: {str(e)}\n")
            sys.stdout.flush()

    # STEP 2: Fallback a dati sessione se Supabase non disponibile
    if not data and ('processed_data' in session or 'preview_data' in session):
        sys.stdout.write("🔄 FALLBACK: Caricamento dati da sessione...\n")
        sys.stdout.flush()

    # Verifica che il file esista
    if 'file_path' in session:
        file_path = session.get('file_path')
        if not file_path:
            sys.stdout.write("ERRORE: Percorso file vuoto o None\n")
            sys.stdout.flush()
            flash('Percorso file non valido. Carica nuovamente il file.', 'error')
            session.clear()
            return redirect(url_for('index'))

    # Usa i dati elaborati se disponibili, altrimenti usa i dati di anteprima
    data = []

    # Se c'è un percorso per i dati elaborati, leggi dal file
    if 'processed_data_path' in session:
        processed_data_path = session['processed_data_path']
        sys.stdout.write(f"Lettura dati elaborati da file: {processed_data_path}\n")

        try:
            if os.path.exists(processed_data_path):
                with open(processed_data_path, 'r', encoding='utf-8') as f:
                    # Import json già presente nella sezione top-level
                    data = json.load(f)
                sys.stdout.write(f"Dati elaborati letti con successo: {len(data)} record\n")
            else:
                sys.stdout.write(f"File dati elaborati non trovato: {processed_data_path}\n")
                # Usa i dati di anteprima come fallback
                data = session.get('preview_data', [])
        except Exception as e:
            sys.stdout.write(f"Errore nella lettura dei dati elaborati: {str(e)}\n")
            # Usa i dati di anteprima come fallback
            data = session.get('preview_data', [])
    else:
        # Usa i dati di anteprima se non ci sono dati elaborati
        data = session.get('preview_data', [])
        sys.stdout.write(f"Usando dati di anteprima: {len(data)} record\n")

    filename = session.get('original_filename', session.get('filename', ''))  # Usa il nome originale se disponibile
    file_type = session.get('file_type', 'generico')

    # Ottieni le colonne dai dati
    columns = []
    if data and len(data) > 0:
        columns = list(data[0].keys())

    # Stampa informazioni dettagliate sul file
    sys.stdout.write(f"Grafici Interattivi - Nome file originale: {session.get('original_filename', 'non disponibile')}\n")
    sys.stdout.write(f"Grafici Interattivi - Nome file univoco: {session.get('filename', 'non disponibile')}\n")
    sys.stdout.write(f"Grafici Interattivi - Tipo file: {file_type}\n")
    sys.stdout.write(f"Grafici Interattivi - Numero colonne: {len(columns)}\n")
    sys.stdout.flush()

    # Salva i dati nella sessione per l'accesso dall'API
    session['data'] = data
    session['columns'] = columns

    return render_template('interactive_charts.html',
                          data=data,
                          columns=columns,
                          filename=filename,
                          file_type=file_type)

# Route per la chat AI - MODERNIZZATA SUPABASE-FIRST
@app.route('/chat')
def chat():
    """
    Chat AI modernizzata con approccio Supabase-first.
    Allineata con dashboard standard per consistenza.
    """
    try:
        import sys

        sys.stdout.write("=== CHAT AI SUPABASE-FIRST ===\n")
        sys.stdout.flush()

        # Inizializza variabili
        data = []
        filename = 'Chat AI'
        file_type = 'chat'
        data_source = 'none'
        columns = []

        # PRIORITÀ 1: Dati da Supabase (approccio moderno)
        try:
            from advanced_database_manager import AdvancedDatabaseManager
            from supabase_integration import SupabaseManager

            supabase_manager = SupabaseManager()
            if supabase_manager.is_connected:
                db_manager = AdvancedDatabaseManager(supabase_manager)

                # Ottieni dati normalizzati da Supabase
                supabase_data = db_manager.get_normalized_activities(limit=1000)

                if supabase_data and len(supabase_data) > 0:
                    data = supabase_data
                    data_source = 'supabase'
                    filename = 'Dati Supabase'
                    file_type = 'normalized'

                    # Ottieni colonne
                    if data:
                        columns = list(data[0].keys())

                    sys.stdout.write(f"✅ Dati caricati da Supabase per Chat AI: {len(data)} record\n")
        except Exception as e:
            sys.stdout.write(f"❌ Errore caricamento Supabase per Chat AI: {str(e)}\n")
            sys.stdout.flush()

        # FALLBACK: Dati da sessione se disponibili
        if not data and 'processed_data' in session:
            try:
                data = session['processed_data']
                data_source = 'session'
                filename = session.get('filename', 'Dati Sessione')
                file_type = session.get('file_type', 'session')

                if data:
                    columns = list(data[0].keys()) if data else []
                    sys.stdout.write(f"✅ Dati caricati da sessione per Chat AI: {len(data)} record\n")
            except Exception as e:
                sys.stdout.write(f"❌ Errore caricamento sessione per Chat AI: {str(e)}\n")
                sys.stdout.flush()

        # Log finale
        sys.stdout.write(f"📱 Chat AI - Fonte: {data_source}, Record: {len(data)}, Tipo: {file_type}\n")
        sys.stdout.flush()

        return render_template('chat.html',
                              data=data,
                              columns=columns,
                              filename=filename,
                              file_type=file_type,
                              data_source=data_source)

    except Exception as e:
        import traceback
        sys.stdout.write(f"❌ Errore Chat AI: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        flash(f'Errore nel caricamento della chat: {str(e)}', 'error')
        return redirect(url_for('index'))

# API per ottenere i dati elaborati dal server MCP
@app.route('/api/processed_data')
def get_processed_data():
    """API per ottenere i dati elaborati dal server MCP."""
    import sys
    import json
    import math

    sys.stdout.write("=== API GET_PROCESSED_DATA ===\n")
    sys.stdout.flush()

    # Verifica se c'è un ID file MCP in sessione
    if 'mcp_file_id' not in session:
        sys.stdout.write("Nessun ID file MCP in sessione\n")
        sys.stdout.flush()
        return jsonify({'error': 'Nessun file elaborato disponibile'})

    mcp_file_id = session['mcp_file_id']
    sys.stdout.write(f"ID file MCP: {mcp_file_id}\n")
    sys.stdout.flush()

    try:
        # Ottieni i dati elaborati dal server MCP
        processed_data = mcp_client.get_processed_data(mcp_file_id)

        # Verifica se ci sono errori
        if 'error' in processed_data:
            sys.stdout.write(f"Errore dal server MCP: {processed_data['error']}\n")
            sys.stdout.flush()

            # Se c'è un errore, prova a usare i dati elaborati locali
            if 'processed_data_path' in session:
                processed_data_path = session['processed_data_path']
                sys.stdout.write(f"Tentativo di lettura dati elaborati locali: {processed_data_path}\n")

                if os.path.exists(processed_data_path):
                    with open(processed_data_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    sys.stdout.write(f"Dati elaborati locali letti con successo: {len(data)} record\n")

                    # Pulisci i dati prima di inviarli al client
                    clean_data = []
                    for item in data:
                        clean_item = {}
                        for key, value in item.items():
                            # Converti NaN e infinito in null
                            if isinstance(value, float) and (math.isnan(value) or math.isinf(value)):
                                clean_item[key] = None
                            else:
                                clean_item[key] = value
                        clean_data.append(clean_item)

                    return jsonify({
                        'data': clean_data,
                        'source': 'local',
                        'message': 'Dati elaborati locali'
                    })
                else:
                    sys.stdout.write(f"File dati elaborati locali non trovato: {processed_data_path}\n")

            # Se non ci sono dati elaborati locali, restituisci l'errore
            return jsonify({
                'error': processed_data['error'],
                'message': 'Impossibile ottenere i dati elaborati dal server MCP'
            })

        # Se i dati sono stati ottenuti con successo, restituiscili
        sys.stdout.write(f"Dati elaborati ottenuti con successo dal server MCP: {len(processed_data.get('data', []))} record\n")
        sys.stdout.flush()

        return jsonify({
            'data': processed_data.get('data', []),
            'source': 'mcp',
            'message': 'Dati elaborati ottenuti dal server MCP',
            'metadata': processed_data.get('metadata', {})
        })

    except Exception as e:
        sys.stdout.write(f"Errore nell'ottenere i dati elaborati: {str(e)}\n")
        sys.stdout.flush()

        # In caso di errore, prova a usare i dati elaborati locali
        if 'processed_data_path' in session:
            processed_data_path = session['processed_data_path']
            sys.stdout.write(f"Tentativo di lettura dati elaborati locali: {processed_data_path}\n")

            if os.path.exists(processed_data_path):
                with open(processed_data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                sys.stdout.write(f"Dati elaborati locali letti con successo: {len(data)} record\n")

                # Pulisci i dati prima di inviarli al client
                clean_data = []
                for item in data:
                    clean_item = {}
                    for key, value in item.items():
                        # Converti NaN e infinito in null
                        if isinstance(value, float) and (math.isnan(value) or math.isinf(value)):
                            clean_item[key] = None
                        else:
                            clean_item[key] = value
                    clean_data.append(clean_item)

                return jsonify({
                    'data': clean_data,
                    'source': 'local',
                    'message': 'Dati elaborati locali (fallback)'
                })

        # Se non ci sono dati elaborati locali, restituisci l'errore
        return jsonify({
            'error': str(e),
            'message': 'Errore nell\'ottenere i dati elaborati'
        })

# API per ottenere i modelli LLM disponibili
@app.route('/api/llm/models')
def get_llm_models():
    import sys
    import logging
    logger = logging.getLogger(__name__)

    logger.info("=== API GET_LLM_MODELS CHIAMATA ===")
    sys.stdout.write("=== API GET_LLM_MODELS CHIAMATA ===\n")
    sys.stdout.flush()

    # Ottieni i parametri di filtro dalla query string
    include_free = request.args.get('include_free', 'true').lower() == 'true'
    include_paid = request.args.get('include_paid', 'true').lower() == 'true'
    only_free_quota = request.args.get('only_free_quota', 'false').lower() == 'true'

    sys.stdout.write(f"Filtri: include_free={include_free}, include_paid={include_paid}, only_free_quota={only_free_quota}\n")
    sys.stdout.flush()

    # Verifica se OpenRouter è disponibile
    if not openrouter_client.is_available:
        logger.warning("OpenRouter non disponibile")
        return jsonify({
            'error': 'OpenRouter non disponibile',
            'message': 'Impossibile ottenere i modelli LLM'
        })

    # Ottieni i modelli disponibili con i filtri
    logger.info("Richiesta modelli a OpenRouter...")

    models = openrouter_client.get_models(include_free=include_free, include_paid=include_paid)

    logger.info("Modelli ricevuti da OpenRouter: %s", len(models))
    if models:
        logger.info("Primi 3 modelli: %s", [m.get('id', 'unknown') for m in models[:3]])

    # Filtra ulteriormente per modelli con quote gratuite se richiesto
    if only_free_quota:
        models = [model for model in models if model.get('has_free_quota', False)]

    # Filtra e formatta i modelli per il frontend
    formatted_models = []
    for model in models:
        formatted_models.append({
            'id': model.get('id', ''),
            'name': model.get('name', ''),
            'description': model.get('description', ''),
            'context_length': model.get('context_length', 0),
            'is_free': model.get('is_free', False),
            'has_free_quota': model.get('has_free_quota', False),
            'pricing': {
                'prompt': model.get('pricing', {}).get('prompt', 0),
                'completion': model.get('pricing', {}).get('completion', 0)
            }
        })

    # Ordina i modelli: prima quelli gratuiti, poi quelli con quote gratuite, infine gli altri
    formatted_models.sort(key=lambda x: (not x['is_free'], not x['has_free_quota']))

    return jsonify({
        'models': formatted_models
    })

# API per inviare un messaggio alla chat LLM
@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    sys.stdout.write("=== API SEND_CHAT_MESSAGE CHIAMATA ===\n")
    sys.stdout.flush()

    # Ottieni i dati dalla richiesta
    data = request.json
    if not data:
        return jsonify({
            'error': 'Dati mancanti',
            'message': 'La richiesta non contiene dati'
        }), 400

    # Estrai i parametri
    message = data.get('message', '')
    model_id = data.get('model_id', 'anthropic/claude-3-haiku')

    if not message:
        return jsonify({
            'error': 'Messaggio mancante',
            'message': 'Il messaggio non può essere vuoto'
        }), 400

    # Verifica se OpenRouter è disponibile
    if not openrouter_client.is_available:
        return jsonify({
            'error': 'OpenRouter non disponibile',
            'message': 'Impossibile inviare il messaggio'
        }), 503

    try:
        # Verifica se c'è un file elaborato in sessione
        file_id = session.get('mcp_file_id')

        if file_id:
            # Invia la richiesta all'MCP per elaborare il messaggio con il contesto dei dati
            response = mcp_client.process_llm_query(
                file_id=file_id,
                query=message,
                model_id=model_id
            )

            # Verifica se ci sono errori
            if 'error' in response:
                # Se c'è un errore dall'MCP, prova a usare direttamente OpenRouter senza contesto
                sys.stdout.write("Errore dall'MCP: %s. Utilizzo diretto di OpenRouter.\n" % response['error'])
                sys.stdout.flush()

                # Crea i messaggi per OpenRouter
                messages = [
                    {"role": "system", "content": "Sei un assistente AI esperto in analisi dati aziendali. Rispondi in modo conciso e professionale."},
                    {"role": "user", "content": message}
                ]

                # Invia la richiesta a OpenRouter
                llm_response = openrouter_client.chat_completion(
                    messages=messages,
                    model=model_id
                )

                # Estrai la risposta
                if 'error' in llm_response:
                    return jsonify({
                        'error': 'Errore nella richiesta a OpenRouter',
                        'message': llm_response['error']
                    }), 500

                assistant_message = llm_response.get('choices', [{}])[0].get('message', {}).get('content', '')

                return jsonify({
                    'response': assistant_message,
                    'source': 'openrouter_direct'
                })

            # Se la risposta dall'MCP è valida, restituiscila
            return jsonify({
                'response': response.get('response', ''),
                'source': 'mcp'
            })

        else:
            # Se non c'è un file elaborato, usa direttamente OpenRouter
            sys.stdout.write("Nessun file elaborato in sessione. Utilizzo diretto di OpenRouter.\n")
            sys.stdout.flush()

            # Crea i messaggi per OpenRouter
            messages = [
                {"role": "system", "content": "Sei un assistente AI esperto in analisi dati aziendali. Rispondi in modo conciso e professionale."},
                {"role": "user", "content": message}
            ]

            # Invia la richiesta a OpenRouter
            llm_response = openrouter_client.chat_completion(
                messages=messages,
                model=model_id
            )

            # Estrai la risposta
            if 'error' in llm_response:
                return jsonify({
                    'error': 'Errore nella richiesta a OpenRouter',
                    'message': llm_response['error']
                }), 500

            assistant_message = llm_response.get('choices', [{}])[0].get('message', {}).get('content', '')

            return jsonify({
                'response': assistant_message,
                'source': 'openrouter_direct'
            })

    except Exception as e:
        sys.stdout.write("Errore nell'invio del messaggio: %s\n" % str(e))
        sys.stdout.flush()

        return jsonify({
            'error': 'Errore interno',
            'message': str(e)
        }), 500

# NOTA: Route /api/wizard/complete già definita più avanti nel file (linea ~4311)
# Questa versione duplicata è stata rimossa per evitare conflitti

# API per verificare stato connessione Supabase
@app.route('/api/supabase/status')
def get_supabase_status():
    """Verifica stato connessione Supabase."""
    sys.stdout.write("=== API SUPABASE STATUS ===\n")
    sys.stdout.flush()

    status = {
        'available': False,
        'connected': False,
        'db_manager': False,
        'test_connection': False,
        'tables': {},
        'error': None
    }

    try:
        # Verifica disponibilità AdvancedDatabaseManager
        if hasattr(app, 'db_manager') and app.db_manager:
            status['db_manager'] = True
            status['connected'] = app.db_manager.is_connected

            if app.db_manager.is_connected:
                # Test connessione
                status['test_connection'] = app.db_manager.supabase_manager.test_connection()

                if status['test_connection']:
                    status['available'] = True

                    # Verifica tabelle principali
                    try:
                        normalized_data = app.db_manager.get_normalized_activities()
                        status['tables']['normalized_activities'] = len(normalized_data) if normalized_data else 0

                        technicians = app.db_manager.get_master_technicians()
                        status['tables']['master_technicians'] = len(technicians) if technicians else 0

                        clients = app.db_manager.get_master_clients()
                        status['tables']['master_clients'] = len(clients) if clients else 0

                    except Exception as table_error:
                        status['error'] = f"Errore accesso tabelle: {str(table_error)}"

        sys.stdout.write(f"Status Supabase: {status}\n")
        sys.stdout.flush()

    except Exception as e:
        status['error'] = str(e)
        sys.stdout.write(f"Errore status Supabase: {str(e)}\n")
        sys.stdout.flush()

    return jsonify(status)

# API per ottenere i dati della dashboard - NUOVO ENDPOINT SUPABASE-FIRST
@app.route('/api/dashboard_data')
def get_dashboard_data():
    """
    API per ottenere i dati della dashboard standard.
    Approccio Supabase-first con fallback intelligente.
    """
    sys.stdout.write("=== API GET_DASHBOARD_DATA SUPABASE-FIRST ===\n")
    sys.stdout.flush()

    try:
        # Inizializza variabili
        data = []
        stats = {}
        data_source_used = 'unknown'

        # STRATEGIA SUPABASE-FIRST: Priorità assoluta a Supabase
        if hasattr(app, 'db_manager') and app.db_manager:
            try:
                sys.stdout.write("🚀 SUPABASE: Caricamento dati dashboard da AdvancedDatabaseManager...\n")
                sys.stdout.flush()

                # Ottieni dati elaborati da Supabase
                supabase_data = app.db_manager.get_processed_data()

                if supabase_data and len(supabase_data) > 0:
                    data = supabase_data
                    data_source_used = 'supabase'

                    # Calcola statistiche dai dati Supabase
                    stats = app.db_manager.calculate_statistics(data)

                    sys.stdout.write("✅ SUPABASE: %s record caricati con successo\n" % len(data))
                    sys.stdout.flush()
                else:
                    sys.stdout.write("⚠️ SUPABASE: Nessun dato trovato\n")
                    sys.stdout.flush()

            except Exception as e:
                sys.stdout.write(f"❌ SUPABASE: Errore caricamento dati: {str(e)}\n")
                sys.stdout.flush()

        # FALLBACK 1: Dati da sessione
        if not data and 'processed_data' in session:
            try:
                sys.stdout.write("🔄 FALLBACK: Caricamento dati da sessione...\n")
                sys.stdout.flush()

                data = session['processed_data']
                data_source_used = 'session'

                # Calcola statistiche di base
                if data:
                    stats = {
                        'total_records': len(data),
                        'total_hours': sum(float(row.get('durata', 0)) for row in data if row.get('durata')),
                        'unique_technicians': len(set(row.get('tecnico', '') for row in data if row.get('tecnico'))),
                        'date_range': {
                            'start': min(row.get('data', '') for row in data if row.get('data')),
                            'end': max(row.get('data', '') for row in data if row.get('data'))
                        }
                    }

                sys.stdout.write(f"✅ SESSIONE: {len(data)} record caricati\n")
                sys.stdout.flush()

            except Exception as e:
                sys.stdout.write(f"❌ SESSIONE: Errore caricamento: {str(e)}\n")
                sys.stdout.flush()

        # FALLBACK 2: Dati demo se nessun dato disponibile
        if not data:
            sys.stdout.write("🎭 DEMO: Generazione dati demo per dashboard...\n")
            sys.stdout.flush()

            data_source_used = 'demo'
            data = [
                {
                    'data': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                    'tecnico': f'Tecnico {(i % 3) + 1}',
                    'cliente': f'Cliente {(i % 5) + 1}',
                    'durata': round(2 + (i % 6) * 0.5, 1),
                    'tipo_attivita': ['Assistenza', 'Manutenzione', 'Installazione'][i % 3]
                }
                for i in range(20)
            ]

            stats = {
                'total_records': len(data),
                'total_hours': sum(row['durata'] for row in data),
                'unique_technicians': 3,
                'unique_clients': 5,
                'date_range': {
                    'start': (datetime.now() - timedelta(days=19)).strftime('%Y-%m-%d'),
                    'end': datetime.now().strftime('%Y-%m-%d')
                }
            }

        # Log finale
        sys.stdout.write(f"📊 Dashboard data - Fonte: {data_source_used}, Record: {len(data)}\n")
        sys.stdout.flush()

        return jsonify({
            'success': True,
            'data': data,
            'stats': stats,
            'data_source': data_source_used,
            'record_count': len(data),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        sys.stdout.write(f"❌ Errore API dashboard_data: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        return jsonify({
            'success': False,
            'error': str(e),
            'data': [],
            'stats': {},
            'data_source': 'error'
        }), 500

# API per configurazione dipendenti - RIMOSSA (GIÀ REGISTRATA ANTICIPATAMENTE)
print("🔧 OTTIMIZZAZIONE: Route /api/config/employees già registrata anticipatamente, saltando...")

# DEBUG: Verifica route Flask registrate
@app.route('/api/debug/flask-routes', methods=['GET'])
def debug_flask_routes():
    """Debug: Lista tutte le route Flask registrate."""
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': str(rule)
        })
    return jsonify({
        'success': True,
        'total_routes': len(routes),
        'routes': routes
    })

# RIMOSSA: Funzione duplicata per evitare conflitti con registrazione anticipata
# La route /api/config/employees è già registrata anticipatamente all'inizio del file

# API per configurazione veicoli - NUOVO ENDPOINT SUPABASE-FIRST
@app.route('/api/config/vehicles', methods=['GET'])
def get_config_vehicles():
    """
    API per ottenere la lista dei veicoli configurati.
    Approccio Supabase-first con fallback intelligente.
    """
    sys.stdout.write("=== API GET_CONFIG_VEHICLES SUPABASE-FIRST ===\n")
    sys.stdout.flush()

    try:
        vehicles = []
        data_source_used = 'unknown'

        # STRATEGIA SUPABASE-FIRST: Priorità assoluta a Supabase con lazy loading
        if init_supabase_systems_lazy() and hasattr(app, 'db_manager') and app.db_manager:
            try:
                sys.stdout.write("🚀 SUPABASE: Caricamento veicoli da AdvancedDatabaseManager...\n")
                sys.stdout.flush()

                # Ottieni veicoli da Supabase (usando master_vehicles o simile)
                # Per ora usiamo un approccio generico
                supabase_vehicles = []

                # Prova a ottenere veicoli da configurazioni Supabase
                try:
                    vehicle_configs = app.db_manager.get_configurations('vehicles')
                    if vehicle_configs:
                        supabase_vehicles = vehicle_configs
                except Exception:  # pylint: disable=broad-except
                    pass

                if supabase_vehicles and len(supabase_vehicles) > 0:
                    vehicles = supabase_vehicles
                    data_source_used = 'supabase'

                    sys.stdout.write(f"✅ SUPABASE: {len(vehicles)} veicoli caricati con successo\n")
                    sys.stdout.flush()
                else:
                    sys.stdout.write("ℹ️ SUPABASE: Nessun veicolo trovato\n")
                    sys.stdout.flush()

            except Exception as e:
                sys.stdout.write(f"❌ SUPABASE: Errore caricamento veicoli: {str(e)}\n")
                sys.stdout.flush()

        # FALLBACK 1: ConfigManager legacy
        if not vehicles:
            try:
                sys.stdout.write("🔄 FALLBACK: Caricamento veicoli da ConfigManager...\n")
                sys.stdout.flush()

                legacy_vehicles = config_manager.get_vehicle_settings()

                if legacy_vehicles:
                    # Converti formato legacy in formato standard
                    vehicles = []
                    for vehicle_name, vehicle_data in legacy_vehicles.items():
                        vehicle = {
                            'name': vehicle_name,
                            'fuel_consumption': vehicle_data.get('fuel_consumption', 0),
                            'daily_cost': vehicle_data.get('daily_cost', 0),
                            'notes': vehicle_data.get('notes', ''),
                            'source': 'config_manager'
                        }
                        vehicles.append(vehicle)

                    data_source_used = 'config_manager'
                    sys.stdout.write(f"✅ FALLBACK: {len(vehicles)} veicoli da ConfigManager\n")
                    sys.stdout.flush()

            except Exception as e:
                sys.stdout.write(f"❌ FALLBACK: Errore ConfigManager: {str(e)}\n")
                sys.stdout.flush()

        # FALLBACK 2: Dati demo se nessun veicolo disponibile
        if not vehicles:
            sys.stdout.write("🎭 DEMO: Generazione veicoli demo...\n")
            sys.stdout.flush()

            data_source_used = 'demo'
            vehicles = [
                {
                    'name': 'Fiat Ducato',
                    'fuel_consumption': 8.5,
                    'daily_cost': 45.0,
                    'notes': 'Furgone principale - Demo',
                    'source': 'demo'
                },
                {
                    'name': 'Volkswagen Crafter',
                    'fuel_consumption': 9.2,
                    'daily_cost': 50.0,
                    'notes': 'Furgone grande - Demo',
                    'source': 'demo'
                },
                {
                    'name': 'Ford Transit',
                    'fuel_consumption': 7.8,
                    'daily_cost': 40.0,
                    'notes': 'Furgone compatto - Demo',
                    'source': 'demo'
                }
            ]

        # Log finale
        sys.stdout.write(f"🚗 Config vehicles - Fonte: {data_source_used}, Veicoli: {len(vehicles)}\n")
        sys.stdout.flush()

        return jsonify({
            'success': True,
            'vehicles': vehicles,
            'data_source': data_source_used,
            'count': len(vehicles),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        sys.stdout.write(f"❌ Errore API config/vehicles: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        return jsonify({
            'success': False,
            'error': str(e),
            'vehicles': [],
            'data_source': 'error'
        }), 500

# API NUOVA per grafici interattivi - SUPABASE-FIRST COMPLETO
@app.route('/api/supabase/chart_data')
def get_supabase_chart_data():
    """
    Endpoint moderno per grafici interattivi - 100% Supabase-first.
    Sostituisce /api/chart_data legacy con approccio completamente Supabase.
    """
    sys.stdout.write("=== API SUPABASE CHART_DATA - MODERN ===\n")
    sys.stdout.flush()

    # Parametri richiesta
    chart_type = request.args.get('type', 'bar')
    x_column = request.args.get('x_column')
    y_column = request.args.get('y_column')
    group_by = request.args.get('group_by')
    aggregation = request.args.get('aggregation', 'sum')
    data_source = request.args.get('data_source', 'normalized_activities')  # Default tabella normalizzata

    sys.stdout.write(f"📊 Parametri: type={chart_type}, x={x_column}, y={y_column}, source={data_source}\n")
    sys.stdout.flush()

    # Validazione parametri obbligatori
    if not x_column:
        return jsonify({
            'error': 'Parametro x_column mancante',
            'message': 'Seleziona una colonna per l\'asse X',
            'available_columns': _get_available_columns_supabase(),
            'status': 'missing_parameters'
        }), 400

    # Validazione tipo grafico
    valid_types = ['bar', 'line', 'scatter', 'pie', 'heatmap', 'box', 'histogram']
    if chart_type not in valid_types:
        return jsonify({
            'error': f'Tipo grafico non supportato: {chart_type}',
            'supported_types': valid_types
        }), 400

    try:
        # STEP 1: Inizializza sistemi Supabase se necessario
        if not hasattr(app, 'db_manager') or not app.db_manager:
            sys.stdout.write("🔄 Inizializzazione lazy sistemi Supabase...\n")
            sys.stdout.flush()
            init_supabase_systems_lazy()

        # STEP 2: Verifica connessione Supabase
        if not hasattr(app, 'db_manager') or not app.db_manager or not app.db_manager.is_connected:
            return jsonify({
                'error': 'Database Supabase non disponibile',
                'message': 'Verifica la connessione al database',
                'status': 'database_unavailable',
                'debug': {
                    'has_db_manager': hasattr(app, 'db_manager'),
                    'db_manager_exists': app.db_manager is not None if hasattr(app, 'db_manager') else False,
                    'is_connected': app.db_manager.is_connected if hasattr(app, 'db_manager') and app.db_manager else False
                }
            }), 503

        # STEP 3: Recupera dati da Supabase
        data = _get_chart_data_from_supabase(data_source, app.db_manager)

        if not data or len(data) == 0:
            return jsonify({
                'error': 'Nessun dato disponibile',
                'message': f'Nessun record trovato in {data_source}',
                'data_source': data_source,
                'status': 'no_data'
            }), 404

        sys.stdout.write(f"✅ SUPABASE: {len(data)} record recuperati da {data_source}\n")
        sys.stdout.flush()

        # STEP 4: Converti in DataFrame e processa
        df = pd.DataFrame(data)

        # Validazione colonne
        if x_column not in df.columns:
            available_cols = list(df.columns)
            return jsonify({
                'error': f'Colonna {x_column} non trovata',
                'available_columns': available_cols,
                'suggestion': f'Usa una di: {", ".join(available_cols[:5])}'
            }), 400

        # STEP 5: Genera dati grafico
        chart_data = _generate_chart_data_supabase(df, chart_type, x_column, y_column, group_by, aggregation)

        # STEP 6: Arricchisci con metadati Supabase
        chart_data.update({
            'data_source': data_source,
            'record_count': len(data),
            'columns_available': list(df.columns),
            'technicians_count': _count_unique_technicians(data),
            'clients_count': _count_unique_clients(data),
            'timestamp': datetime.now().isoformat(),
            'supabase_first': True
        })

        sys.stdout.write(f"✅ GRAFICO: {chart_type} generato con successo\n")
        sys.stdout.flush()

        return jsonify(chart_data)

    except Exception as e:
        sys.stdout.write(f"❌ ERRORE: {str(e)}\n")
        sys.stdout.flush()

        return jsonify({
            'error': str(e),
            'message': 'Errore nella generazione del grafico',
            'status': 'processing_error'
        }), 500

def _get_available_columns_supabase():
    """Recupera colonne disponibili da Supabase."""
    try:
        if hasattr(app, 'db_manager') and app.db_manager and app.db_manager.is_connected:
            # Prova a recuperare un record per ottenere le colonne
            sample_data = app.db_manager.get_normalized_activities(limit=1)
            if sample_data and len(sample_data) > 0:
                return list(sample_data[0].keys())
    except Exception:  # pylint: disable=broad-except
        pass
    return ['activity_date', 'duration_hours', 'total_cost', 'technician_name', 'client_name']

def _get_chart_data_from_supabase(data_source, db_manager):
    """Recupera dati da Supabase in base alla fonte - VERSIONE SEMPLIFICATA."""
    try:
        sys.stdout.write(f"🔍 Recupero dati da {data_source} (modalità semplificata)...\n")
        sys.stdout.flush()

        if data_source == 'normalized_activities':
            # Query semplificata senza join per evitare errori PGRST200
            result = db_manager.client.table("normalized_activities").select("*").limit(5000).order("activity_date", desc=True).execute()
            if result.data:
                sys.stdout.write(f"✅ Recuperate {len(result.data)} attività normalizzate (senza join)\n")
                sys.stdout.flush()
                return result.data
            return []

        elif data_source == 'normalized_teamviewer':
            # Query semplificata senza join
            result = db_manager.client.table("normalized_teamviewer").select("*").limit(5000).order("session_start", desc=True).execute()
            if result.data:
                sys.stdout.write(f"✅ Recuperate {len(result.data)} sessioni TeamViewer (senza join)\n")
                sys.stdout.flush()
                return result.data
            return []

        elif data_source == 'cross_analysis':
            # Recupera dati separatamente e combina
            activities_result = db_manager.client.table("normalized_activities").select("*").limit(2500).execute()
            teamviewer_result = db_manager.client.table("normalized_teamviewer").select("*").limit(2500).execute()

            combined = []
            if activities_result.data:
                combined.extend(activities_result.data)
            if teamviewer_result.data:
                combined.extend(teamviewer_result.data)

            sys.stdout.write(f"✅ Recuperati {len(combined)} record combinati per analisi incrociata\n")
            sys.stdout.flush()
            return combined

        else:
            # Default: attività normalizzate
            result = db_manager.client.table("normalized_activities").select("*").limit(5000).order("activity_date", desc=True).execute()
            if result.data:
                sys.stdout.write(f"✅ Recuperate {len(result.data)} attività (default)\n")
                sys.stdout.flush()
                return result.data
            return []

    except Exception as e:
        sys.stdout.write(f"❌ Errore recupero dati {data_source}: {str(e)}\n")
        sys.stdout.flush()
        return []

def _generate_chart_data_supabase(df, chart_type, x_column, y_column, group_by, aggregation):
    """Genera dati grafico ottimizzati per Supabase."""
    try:
        # Preprocessing DataFrame
        df = df.copy()

        # Gestione colonne con relazioni Supabase
        if 'master_technicians' in df.columns:
            df['technician_name'] = df['master_technicians'].apply(
                lambda x: x.get('normalized_name', 'N/A') if isinstance(x, dict) else str(x)
            )

        if 'master_clients' in df.columns:
            df['client_name'] = df['master_clients'].apply(
                lambda x: x.get('normalized_name', 'N/A') if isinstance(x, dict) else str(x)
            )

        # Aggregazione dati
        if y_column and y_column in df.columns:
            if group_by and group_by in df.columns:
                # Aggregazione con raggruppamento
                if aggregation == 'sum':
                    grouped = df.groupby([x_column, group_by])[y_column].sum().reset_index()
                elif aggregation == 'mean':
                    grouped = df.groupby([x_column, group_by])[y_column].mean().reset_index()
                elif aggregation == 'count':
                    grouped = df.groupby([x_column, group_by])[y_column].count().reset_index()
                else:
                    grouped = df.groupby([x_column, group_by])[y_column].sum().reset_index()

                # Genera serie multiple per grafico
                chart_data = {
                    'data': [],
                    'layout': {
                        'title': f'{chart_type.title()} - {x_column} vs {y_column}',
                        'xaxis': {'title': x_column},
                        'yaxis': {'title': y_column}
                    }
                }

                for group_value in grouped[group_by].unique():
                    group_data = grouped[grouped[group_by] == group_value]
                    chart_data['data'].append({
                        'x': group_data[x_column].tolist(),
                        'y': group_data[y_column].tolist(),
                        'name': str(group_value),
                        'type': chart_type
                    })
            else:
                # Aggregazione semplice
                if aggregation == 'sum':
                    aggregated = df.groupby(x_column)[y_column].sum().reset_index()
                elif aggregation == 'mean':
                    aggregated = df.groupby(x_column)[y_column].mean().reset_index()
                elif aggregation == 'count':
                    aggregated = df.groupby(x_column)[y_column].count().reset_index()
                else:
                    aggregated = df.groupby(x_column)[y_column].sum().reset_index()

                chart_data = {
                    'data': [{
                        'x': aggregated[x_column].tolist(),
                        'y': aggregated[y_column].tolist(),
                        'type': chart_type
                    }],
                    'layout': {
                        'title': f'{chart_type.title()} - {x_column} vs {y_column}',
                        'xaxis': {'title': x_column},
                        'yaxis': {'title': y_column}
                    }
                }
        else:
            # Solo conteggio per x_column
            counts = df[x_column].value_counts().reset_index()
            counts.columns = [x_column, 'count']

            chart_data = {
                'data': [{
                    'x': counts[x_column].tolist(),
                    'y': counts['count'].tolist(),
                    'type': chart_type
                }],
                'layout': {
                    'title': f'{chart_type.title()} - Distribuzione {x_column}',
                    'xaxis': {'title': x_column},
                    'yaxis': {'title': 'Conteggio'}
                }
            }

        return chart_data

    except Exception as e:
        sys.stdout.write(f"❌ Errore generazione grafico: {str(e)}\n")
        sys.stdout.flush()
        raise

def _count_unique_technicians(data):
    """Conta tecnici unici nei dati - VERSIONE SEMPLIFICATA."""
    try:
        technicians = set()
        for record in data:
            # Prova prima con join (se disponibile)
            if 'master_technicians' in record and record['master_technicians']:
                tech_data = record['master_technicians']
                if isinstance(tech_data, dict):
                    technicians.add(tech_data.get('normalized_name', ''))
            # Altrimenti usa technician_id o campi diretti
            elif 'technician_id' in record and record['technician_id']:
                technicians.add(f"tech_{record['technician_id']}")
            elif 'technician_name' in record and record['technician_name']:
                technicians.add(record['technician_name'])
        return len(technicians)
    except Exception:  # pylint: disable=broad-except
        return 0

def _count_unique_clients(data):
    """Conta clienti unici nei dati - VERSIONE SEMPLIFICATA."""
    try:
        clients = set()
        for record in data:
            # Prova prima con join (se disponibile)
            if 'master_clients' in record and record['master_clients']:
                client_data = record['master_clients']
                if isinstance(client_data, dict):
                    clients.add(client_data.get('normalized_name', ''))
            # Altrimenti usa client_id o campi diretti
            elif 'client_id' in record and record['client_id']:
                clients.add("client_%s" % record['client_id'])
            elif 'client_name' in record and record['client_name']:
                clients.add(record['client_name'])
        return len(clients)
    except Exception:  # pylint: disable=broad-except
        return 0

# API per ottenere i dati per i grafici interattivi - VERSIONE LEGACY (DEPRECATA)
# NOTA: Questo endpoint è deprecato. Utilizzare /api/supabase/chart_data
@app.route('/api/chart_data')
def get_chart_data_legacy():
    sys.stdout.write("=== API GET_CHART_DATA LEGACY (DEPRECATO) ===\n")
    sys.stdout.write("⚠️ ATTENZIONE: Endpoint deprecato. Utilizzare /api/supabase/chart_data\n")
    sys.stdout.flush()

    # Ottieni i parametri dalla richiesta
    chart_type = request.args.get('type', 'bar')
    x_column = request.args.get('x_column')
    y_column = request.args.get('y_column')
    group_by = request.args.get('group_by')
    aggregation = request.args.get('aggregation', 'sum')
    data_source = request.args.get('data_source', 'supabase')  # Default Supabase

    sys.stdout.write("📊 Parametri: type=%s, x_column=%s, y_column=%s\n" % (chart_type, x_column, y_column))
    sys.stdout.write("📊 Opzioni: group_by=%s, aggregation=%s, data_source=%s\n" % (group_by, aggregation, data_source))
    sys.stdout.flush()

    # Validazione parametri con fallback intelligente
    if not x_column:
        sys.stdout.write("⚠️ Parametro x_column mancante, fornendo informazioni disponibili...\n")
        sys.stdout.flush()

        # Invece di errore, fornisci informazioni sulle colonne disponibili
        try:
            # Prova a ottenere dati per mostrare colonne disponibili
            available_columns = []
            sample_data = None

            # Controlla Supabase
            if hasattr(app, 'db_manager') and app.db_manager:
                try:
                    if app.db_manager.is_connected:
                        normalized_data = app.db_manager.get_normalized_activities()
                        if normalized_data and len(normalized_data) > 0:
                            sample_data = normalized_data[:1]  # Solo un record per esempio
                            available_columns = list(normalized_data[0].keys()) if normalized_data else []
                except Exception:  # pylint: disable=broad-except
                    pass

            # Fallback a dati sessione
            if not available_columns:
                for source_key in ['processed_data', 'preview_data', 'data']:
                    if source_key in session and session[source_key]:
                        data_sample = session[source_key]
                        if data_sample and len(data_sample) > 0:
                            available_columns = list(data_sample[0].keys()) if isinstance(data_sample[0], dict) else []
                            sample_data = data_sample[:1]
                            break

            return jsonify({
                'error': 'Parametro x_column mancante',
                'message': 'Seleziona una colonna per l\'asse X',
                'help': {
                    'available_columns': available_columns,
                    'sample_data': sample_data,
                    'usage': 'Aggiungi ?x_column=nome_colonna alla URL',
                    'example': f'/api/chart_data?x_column={available_columns[0] if available_columns else "data"}&type=bar'
                },
                'status': 'missing_parameters'
            })

        except Exception as e:
            return jsonify({
                'error': 'Parametro x_column mancante',
                'message': 'Seleziona una colonna per l\'asse X',
                'help': {
                    'usage': 'Aggiungi ?x_column=nome_colonna alla URL',
                    'example': '/api/chart_data?x_column=data&type=bar'
                },
                'status': 'missing_parameters'
            })

    if chart_type not in ['bar', 'line', 'scatter', 'pie', 'heatmap', 'box', 'histogram']:
        sys.stdout.write("❌ Errore: Tipo di grafico non supportato: %s\n" % chart_type)
        sys.stdout.flush()
        return jsonify({
            'error': 'Tipo di grafico non supportato: %s' % chart_type,
            'supported_types': ['bar', 'line', 'scatter', 'pie', 'heatmap', 'box', 'histogram']
        })

    try:
        # STRATEGIA SUPABASE-FIRST: Priorità assoluta a Supabase
        data = None
        data_source_used = 'unknown'

        # STEP 1: SUPABASE (Priorità Massima)
        if data_source in ['supabase', 'processed'] and hasattr(app, 'db_manager') and app.db_manager:
            try:
                sys.stdout.write("🚀 SUPABASE: Caricamento dati da AdvancedDatabaseManager...\n")
                sys.stdout.flush()

                # Test connessione rapido
                if app.db_manager.is_connected and app.db_manager.supabase_manager.test_connection():
                    # Ottieni dati normalizzati con arricchimento automatico
                    normalized_data = app.db_manager.get_normalized_activities()

                    if normalized_data and len(normalized_data) > 0:
                        data = normalized_data
                        data_source_used = 'supabase'
                        sys.stdout.write("✅ SUPABASE: %s record caricati con successo\n" % len(data))

                        # Arricchimento intelligente con master entities
                        try:
                            technicians = app.db_manager.get_master_technicians()
                            clients = app.db_manager.get_master_clients()

                            enriched_count = 0
                            for record in data:
                                # Smart matching per dipendenti
                                for field in ['tecnico', 'dipendente', 'operatore', 'user']:
                                    if field in record and record[field]:
                                        original_value = str(record[field]).strip()
                                        for tech in technicians:
                                            if (tech.get('name', '').lower() in original_value.lower() or
                                                original_value.lower() in tech.get('name', '').lower() or
                                                tech.get('code', '').lower() == original_value.lower()):
                                                record['%s_enriched' % field] = tech.get('name', original_value)
                                                record['%s_code' % field] = tech.get('code', '')
                                                enriched_count += 1
                                                break

                                # Smart matching per clienti
                                for field in ['cliente', 'client', 'azienda']:
                                    if field in record and record[field]:
                                        original_value = str(record[field]).strip()
                                        for client in clients:
                                            if (client.get('name', '').lower() in original_value.lower() or
                                                original_value.lower() in client.get('name', '').lower() or
                                                client.get('code', '').lower() == original_value.lower()):
                                                record['%s_enriched' % field] = client.get('name', original_value)
                                                record['%s_code' % field] = client.get('code', '')
                                                enriched_count += 1
                                                break

                            sys.stdout.write("✅ SUPABASE: %s campi arricchiti con master entities\n" % enriched_count)
                            sys.stdout.flush()

                        except Exception as enrich_error:
                            sys.stdout.write("⚠️ SUPABASE: Errore arricchimento (continuando): %s\n" % str(enrich_error))
                            sys.stdout.flush()
                    else:
                        sys.stdout.write("ℹ️ SUPABASE: Nessun dato normalizzato disponibile\n")
                        sys.stdout.flush()
                else:
                    sys.stdout.write("⚠️ SUPABASE: Connessione non disponibile\n")
                    sys.stdout.flush()

            except Exception as e:
                sys.stdout.write("❌ SUPABASE: Errore critico: %s\n" % str(e))
                sys.stdout.flush()

        # STEP 2: FALLBACK LEGACY (Solo se Supabase non disponibile)
        if not data:
            sys.stdout.write("🔄 LEGACY: Fallback a dati locali/sessione...\n")
            data_source_used = 'legacy'

            # Strategia semplificata per fallback
            fallback_sources = [
                ('processed_data', 'Dati elaborati sessione'),
                ('preview_data', 'Dati anteprima sessione'),
                ('data', 'Dati grezzi sessione')
            ]

            for source_key, source_desc in fallback_sources:
                if source_key in session and session[source_key]:
                    data = session[source_key]
                    sys.stdout.write("✅ LEGACY: %s record da %s\n" % (len(data), source_desc))
                    break

            # Ultimo tentativo: MCP Server
            if not data and 'mcp_file_id' in session:
                try:
                    mcp_file_id = session['mcp_file_id']
                    processed_data = mcp_client.get_processed_data(mcp_file_id)
                    if 'error' not in processed_data:
                        data = processed_data.get('data', [])
                        sys.stdout.write("✅ LEGACY: %s record da MCP Server\n" % len(data))
                except Exception as mcp_error:
                    sys.stdout.write("⚠️ LEGACY: Errore MCP: %s\n" % str(mcp_error))

        # Verifica finale disponibilità dati
        if not data or len(data) == 0:
            sys.stdout.write("❌ ERRORE: Nessun dato disponibile da tutte le fonti\n")
            sys.stdout.flush()
            return jsonify({
                'error': 'Nessun dato disponibile per generare il grafico',
                'message': 'Carica un file o verifica la connessione Supabase',
                'sources_checked': ['supabase', 'session', 'mcp']
            })

        sys.stdout.write("✅ SUCCESSO: %s record pronti per elaborazione (fonte: %s)\n" % (len(data), data_source_used))
        sys.stdout.flush()

        # Converti i dati in un DataFrame pandas
        sys.stdout.write("Conversione di %s record in DataFrame\n" % len(data))
        sys.stdout.flush()

        df = pd.DataFrame(data)

        sys.stdout.write("DataFrame creato con %s righe e %s colonne\n" % (len(df), len(df.columns)))
        sys.stdout.write("Colonne disponibili: %s\n" % df.columns.tolist())
        sys.stdout.flush()

        # Verifica se le colonne richieste esistono
        if x_column not in df.columns:
            sys.stdout.write("Errore: Colonna %s non trovata nei dati\n" % x_column)
            sys.stdout.write("Colonne disponibili: %s\n" % df.columns.tolist())
            sys.stdout.flush()
            return jsonify({
                'error': 'Colonna %s non trovata nei dati' % x_column,
                'available_columns': df.columns.tolist(),
                'suggestion': 'Verifica il nome della colonna o carica nuovamente il file'
            })

        if y_column and y_column not in df.columns:
            sys.stdout.write("Errore: Colonna %s non trovata nei dati\n" % y_column)
            sys.stdout.flush()
            return jsonify({'error': 'Colonna %s non trovata nei dati' % y_column})

        if group_by and group_by not in df.columns:
            sys.stdout.write("Errore: Colonna %s non trovata nei dati\n" % group_by)
            sys.stdout.flush()
            return jsonify({'error': 'Colonna %s non trovata nei dati' % group_by})

        # Prepara i dati per il grafico
        chart_data = {}

        # Gestisci i diversi tipi di grafico
        if chart_type == 'bar' or chart_type == 'line' or chart_type == 'scatter':
            # Prepara i dati per grafici a barre, linee o scatter
            if group_by:
                # Raggruppa i dati per la colonna di gruppo
                if y_column:
                    # Se è specificata una colonna y, raggruppa per x e group_by e aggrega y
                    grouped = df.groupby([x_column, group_by])[y_column]

                    # Applica l'aggregazione specificata
                    if aggregation == 'sum':
                        grouped = grouped.sum()
                    elif aggregation == 'mean':
                        grouped = grouped.mean()
                    elif aggregation == 'count':
                        grouped = grouped.count()
                    elif aggregation == 'min':
                        grouped = grouped.min()
                    elif aggregation == 'max':
                        grouped = grouped.max()

                    # Converti il risultato in un DataFrame
                    grouped = grouped.reset_index()

                    # Crea una serie per ogni valore unico nella colonna di gruppo
                    traces = []
                    for group_value in grouped[group_by].unique():
                        group_data = grouped[grouped[group_by] == group_value]

                        trace = {
                            'x': group_data[x_column].tolist(),
                            'y': group_data[y_column].tolist(),
                            'type': chart_type,
                            'name': str(group_value)
                        }

                        traces.append(trace)

                    chart_data['data'] = traces
                else:
                    # Se non è specificata una colonna y, conta le occorrenze per ogni combinazione di x e group_by
                    grouped = df.groupby([x_column, group_by]).size().reset_index(name='count')

                    # Crea una serie per ogni valore unico nella colonna di gruppo
                    traces = []
                    for group_value in grouped[group_by].unique():
                        group_data = grouped[grouped[group_by] == group_value]

                        trace = {
                            'x': group_data[x_column].tolist(),
                            'y': group_data['count'].tolist(),
                            'type': chart_type,
                            'name': str(group_value)
                        }

                        traces.append(trace)

                    chart_data['data'] = traces
            else:
                # Senza raggruppamento
                if y_column:
                    # Se è specificata una colonna y, raggruppa per x e aggrega y
                    grouped = df.groupby(x_column)[y_column]

                    # Applica l'aggregazione specificata
                    if aggregation == 'sum':
                        grouped = grouped.sum()
                    elif aggregation == 'mean':
                        grouped = grouped.mean()
                    elif aggregation == 'count':
                        grouped = grouped.count()
                    elif aggregation == 'min':
                        grouped = grouped.min()
                    elif aggregation == 'max':
                        grouped = grouped.max()

                    # Converti il risultato in un DataFrame
                    grouped = grouped.reset_index()

                    trace = {
                        'x': grouped[x_column].tolist(),
                        'y': grouped[y_column].tolist(),
                        'type': chart_type,
                        'name': y_column
                    }

                    chart_data['data'] = [trace]
                else:
                    # Se non è specificata una colonna y, conta le occorrenze per ogni valore di x
                    grouped = df.groupby(x_column).size().reset_index(name='count')

                    trace = {
                        'x': grouped[x_column].tolist(),
                        'y': grouped['count'].tolist(),
                        'type': chart_type,
                        'name': 'Conteggio'
                    }

                    chart_data['data'] = [trace]

        elif chart_type == 'pie':
            # Prepara i dati per grafici a torta
            if y_column:
                # Se è specificata una colonna y, raggruppa per x e aggrega y
                grouped = df.groupby(x_column)[y_column]

                # Applica l'aggregazione specificata
                if aggregation == 'sum':
                    grouped = grouped.sum()
                elif aggregation == 'mean':
                    grouped = grouped.mean()
                elif aggregation == 'count':
                    grouped = grouped.count()
                elif aggregation == 'min':
                    grouped = grouped.min()
                elif aggregation == 'max':
                    grouped = grouped.max()

                # Converti il risultato in un DataFrame
                grouped = grouped.reset_index()

                trace = {
                    'labels': grouped[x_column].tolist(),
                    'values': grouped[y_column].tolist(),
                    'type': 'pie',
                    'name': y_column
                }
            else:
                # Se non è specificata una colonna y, conta le occorrenze per ogni valore di x
                grouped = df.groupby(x_column).size().reset_index(name='count')

                trace = {
                    'labels': grouped[x_column].tolist(),
                    'values': grouped['count'].tolist(),
                    'type': 'pie',
                    'name': 'Conteggio'
                }

            chart_data['data'] = [trace]

        elif chart_type == 'heatmap':
            # Prepara i dati per grafici heatmap
            if not y_column:
                return jsonify({'error': 'Per i grafici heatmap è necessario specificare sia x_column che y_column'})

            # Raggruppa i dati per x e y
            if group_by:
                # Se è specificato un group_by, crea un heatmap per ogni valore di group_by
                traces = []
                for group_value in df[group_by].unique():
                    group_df = df[df[group_by] == group_value]

                    # Crea una tabella pivot
                    pivot = pd.pivot_table(
                        group_df,
                        values=group_by,
                        index=y_column,
                        columns=x_column,
                        aggfunc='count'
                    )

                    # Converti in liste per Plotly
                    trace = {
                        'z': pivot.values.tolist(),
                        'x': pivot.columns.tolist(),
                        'y': pivot.index.tolist(),
                        'type': 'heatmap',
                        'name': str(group_value),
                        'colorscale': 'Viridis'
                    }

                    traces.append(trace)

                chart_data['data'] = traces
            else:
                # Crea una tabella pivot
                pivot = pd.pivot_table(
                    df,
                    values='count' if 'count' in df.columns else df.columns[0],
                    index=y_column,
                    columns=x_column,
                    aggfunc='count'
                )

                # Converti in liste per Plotly
                trace = {
                    'z': pivot.values.tolist(),
                    'x': pivot.columns.tolist(),
                    'y': pivot.index.tolist(),
                    'type': 'heatmap',
                    'colorscale': 'Viridis'
                }

                chart_data['data'] = [trace]

        elif chart_type == 'box':
            # Prepara i dati per grafici box plot
            if not y_column:
                return jsonify({'error': 'Per i grafici box plot è necessario specificare sia x_column che y_column'})

            if group_by:
                # Se è specificato un group_by, crea un box plot per ogni valore di group_by
                traces = []
                for group_value in df[group_by].unique():
                    group_df = df[df[group_by] == group_value]

                    trace = {
                        'x': group_df[x_column].tolist(),
                        'y': group_df[y_column].tolist(),
                        'type': 'box',
                        'name': str(group_value)
                    }

                    traces.append(trace)

                chart_data['data'] = traces
            else:
                # Crea un box plot per ogni valore unico di x_column
                traces = []
                for x_value in df[x_column].unique():
                    x_df = df[df[x_column] == x_value]

                    trace = {
                        'y': x_df[y_column].tolist(),
                        'type': 'box',
                        'name': str(x_value)
                    }

                    traces.append(trace)

                chart_data['data'] = traces

        elif chart_type == 'histogram':
            # Prepara i dati per grafici histogram
            if group_by:
                # Se è specificato un group_by, crea un istogramma per ogni valore di group_by
                traces = []
                for group_value in df[group_by].unique():
                    group_df = df[df[group_by] == group_value]

                    trace = {
                        'x': group_df[x_column].tolist(),
                        'type': 'histogram',
                        'name': str(group_value)
                    }

                    if y_column:
                        trace['y'] = group_df[y_column].tolist()

                    traces.append(trace)

                chart_data['data'] = traces
            else:
                # Crea un istogramma semplice
                trace = {
                    'x': df[x_column].tolist(),
                    'type': 'histogram',
                    'name': x_column
                }

                if y_column:
                    trace['y'] = df[y_column].tolist()

                chart_data['data'] = [trace]

        # Layout moderno con informazioni fonte dati
        source_display = {
            'supabase': '🚀 Supabase (Tempo Reale)',
            'legacy': '📁 Dati Locali',
            'unknown': '❓ Fonte Sconosciuta'
        }.get(data_source_used, '❓ Fonte Sconosciuta')

        chart_data['layout'] = {
            'title': {
                'text': "Grafico %s" % chart_type.capitalize(),
                'subtitle': {
                    'text': "Fonte: %s | %s record elaborati" % (source_display, len(data))
                },
                'font': {'size': 16}
            },
            'xaxis': {
                'title': x_column,
                'showgrid': True,
                'gridcolor': 'rgba(128,128,128,0.2)'
            },
            'margin': {
                'l': 60,
                'r': 30,
                'b': 80,
                't': 80,
                'pad': 4
            },
            'plot_bgcolor': 'rgba(0,0,0,0)',
            'paper_bgcolor': 'rgba(0,0,0,0)',
            'hovermode': 'closest'
        }

        # Asse Y con styling migliorato
        if y_column and chart_type != 'pie':
            chart_data['layout']['yaxis'] = {
                'title': y_column if y_column else 'Conteggio',
                'showgrid': True,
                'gridcolor': 'rgba(128,128,128,0.2)'
            }

        # Metadati estesi per debugging e UI
        chart_data.update({
            'source': data_source_used,
            'source_display': source_display,
            'available_columns': df.columns.tolist(),
            'record_count': len(data),
            'processed_count': len(df),
            'column_types': {
                col: 'numeric' if pd.api.types.is_numeric_dtype(df[col])
                     else 'datetime' if pd.api.types.is_datetime64_any_dtype(df[col])
                     else 'categorical'
                for col in df.columns
            },
            'generation_info': {
                'chart_type': chart_type,
                'x_column': x_column,
                'y_column': y_column,
                'group_by': group_by,
                'aggregation': aggregation
            }
        })

        sys.stdout.write("✅ GRAFICO: %s generato con %s serie\n" % (chart_type, len(chart_data.get('data', []))))
        sys.stdout.write("✅ FONTE: %s (%s → %s record)\n" % (source_display, len(data), len(df)))
        sys.stdout.flush()

        return jsonify(chart_data)

    except Exception as e:
        sys.stdout.write("Errore nella generazione del grafico: %s\n" % str(e))
        sys.stdout.flush()

        traceback.print_exc()

        return jsonify({
            'error': str(e),
            'message': 'Errore nella generazione del grafico'
        })

# ROUTE RIMOSSE - Spostate prima della funzione get_chart_data() per evitare problemi di registrazione
# Le route sono ora correttamente posizionate alle righe 3578-3833

# Cache per i grafici
chart_cache = {}

# API per ottenere i grafici - VERSIONE MODERNIZZATA SUPABASE-FIRST
@app.route('/api/charts/<chart_type>')
def get_chart(chart_type):
    start_time = time.time()
    sys.stdout.write("=== API GET_CHART SUPABASE-FIRST ===\n")
    sys.stdout.write("📊 Tipo grafico: %s\n" % chart_type)
    sys.stdout.flush()

    # Parametri richiesta
    chart_subtype = request.args.get('type', 'default')
    data_source = request.args.get('data_source', 'supabase')  # Default Supabase

    sys.stdout.write("📊 Sottotipo: %s, Fonte: %s\n" % (chart_subtype, data_source))
    sys.stdout.flush()

    # Validazione tipo grafico
    valid_chart_types = ['time_series', 'distribution', 'technician', 'client', 'heatmap', 'duration']
    if chart_type not in valid_chart_types:
        sys.stdout.write("❌ Tipo grafico non supportato: %s\n" % chart_type)
        sys.stdout.flush()
        return jsonify({
            'error': 'Tipo grafico non supportato: %s' % chart_type,
            'supported_types': valid_chart_types
        })

    try:
        # STRATEGIA SUPABASE-FIRST: Priorità assoluta a Supabase
        data = None
        data_source_used = 'unknown'

        # STEP 1: SUPABASE (Priorità Massima)
        if hasattr(app, 'db_manager') and app.db_manager:
            try:
                sys.stdout.write("🚀 SUPABASE: Caricamento dati da AdvancedDatabaseManager...\n")
                sys.stdout.flush()

                # Test connessione rapido
                if app.db_manager.is_connected and app.db_manager.supabase_manager.test_connection():
                    # Ottieni dati normalizzati
                    normalized_data = app.db_manager.get_normalized_activities()

                    if normalized_data and len(normalized_data) > 0:
                        data = normalized_data
                        data_source_used = 'supabase'
                        sys.stdout.write(f"✅ SUPABASE: {len(data)} record caricati\n")

                        # Arricchimento con master entities
                        try:
                            technicians = app.db_manager.get_master_technicians()
                            clients = app.db_manager.get_master_clients()

                            enriched_count = 0
                            for record in data:
                                # Smart matching dipendenti
                                for field in ['tecnico', 'dipendente', 'operatore', 'user', 'Creato da', 'Assegnatario']:
                                    if field in record and record[field]:
                                        original_value = str(record[field]).strip()
                                        for tech in technicians:
                                            if (tech.get('name', '').lower() in original_value.lower() or
                                                original_value.lower() in tech.get('name', '').lower()):
                                                record['%s_enriched' % field] = tech.get('name', original_value)
                                                enriched_count += 1
                                                break

                                # Smart matching clienti
                                for field in ['cliente', 'client', 'azienda']:
                                    if field in record and record[field]:
                                        original_value = str(record[field]).strip()
                                        for client in clients:
                                            if (client.get('name', '').lower() in original_value.lower() or
                                                original_value.lower() in client.get('name', '').lower()):
                                                record['%s_enriched' % field] = client.get('name', original_value)
                                                enriched_count += 1
                                                break

                            sys.stdout.write("✅ SUPABASE: %s campi arricchiti\n" % enriched_count)
                            sys.stdout.flush()

                        except Exception as enrich_error:
                            sys.stdout.write("⚠️ SUPABASE: Errore arricchimento: %s\n" % str(enrich_error))
                            sys.stdout.flush()
                    else:
                        sys.stdout.write("ℹ️ SUPABASE: Nessun dato normalizzato disponibile\n")
                        sys.stdout.flush()
                else:
                    sys.stdout.write("⚠️ SUPABASE: Connessione non disponibile\n")
                    sys.stdout.flush()

            except Exception as e:
                sys.stdout.write("❌ SUPABASE: Errore critico: %s\n" % str(e))
                sys.stdout.flush()

        # STEP 2: FALLBACK LEGACY (Solo se Supabase non disponibile)
        if not data:
            sys.stdout.write("🔄 LEGACY: Fallback a dati locali/sessione...\n")
            data_source_used = 'legacy'

            # Strategia semplificata per fallback
            fallback_sources = [
                ('processed_data', 'Dati elaborati sessione'),
                ('preview_data', 'Dati anteprima sessione'),
                ('data', 'Dati grezzi sessione')
            ]

            for source_key, source_desc in fallback_sources:
                if source_key in session and session[source_key]:
                    data = session[source_key]
                    sys.stdout.write("✅ LEGACY: %s record da %s\n" % (len(data), source_desc))
                    break

        # Verifica finale disponibilità dati
        if not data or len(data) == 0:
            sys.stdout.write("❌ ERRORE: Nessun dato disponibile da tutte le fonti\n")
            sys.stdout.flush()
            return jsonify({
                'error': 'Nessun dato disponibile per generare il grafico',
                'message': 'Carica un file o verifica la connessione Supabase',
                'chart_type': chart_type
            })

        sys.stdout.write("✅ SUCCESSO: %s record pronti (fonte: %s)\n" % (len(data), data_source_used))
        sys.stdout.flush()

        # Converti in DataFrame con ottimizzazioni
        df = pd.DataFrame(data)

        # Limita record per performance
        if len(df) > 1000:
            df = df.sample(1000)
            sys.stdout.write("📊 DataFrame ridotto a %s record per performance\n" % len(df))

        sys.stdout.write("📊 DataFrame: %s righe, %s colonne\n" % (len(df), len(df.columns)))
        sys.stdout.flush()

        # GENERAZIONE GRAFICO MODERNA E SEMPLIFICATA

        # Layout comune moderno
        layout = {
            'margin': {'t': 50, 'r': 30, 'l': 50, 'b': 50},
            'font': {'family': 'Segoe UI, sans-serif', 'size': 12},
            'paper_bgcolor': 'rgba(0,0,0,0)',
            'plot_bgcolor': 'rgba(0,0,0,0)',
            'hovermode': 'closest',
            'title': {
                'font': {'size': 16},
                'x': 0.5,
                'xanchor': 'center'
            }
        }

        # Genera grafico in base al tipo
        fig = None
        chart_title = "Grafico %s" % chart_type.title()

        try:
            if chart_type == 'time_series':
                fig = generate_time_series_chart(df, data_source_used)
            elif chart_type == 'distribution':
                fig = generate_distribution_chart(df, chart_subtype, data_source_used)
            elif chart_type == 'technician':
                fig = generate_technician_chart(df, data_source_used)
            elif chart_type == 'client':
                fig = generate_client_chart(df, data_source_used)
            elif chart_type == 'heatmap':
                fig = generate_heatmap_chart(df, data_source_used)
            elif chart_type == 'duration':
                fig = generate_duration_chart(df, data_source_used)
            else:
                # Fallback: grafico semplice
                fig = generate_fallback_chart(df, chart_type, data_source_used)

            if fig is None:
                raise ValueError("Impossibile generare grafico tipo %s" % chart_type)

            # Applica layout comune
            fig.update_layout(layout)

            # Aggiungi informazioni fonte dati nel titolo
            source_display = {
                'supabase': '🚀 Supabase',
                'legacy': '📁 Locale',
                'unknown': '❓ Sconosciuta'
            }.get(data_source_used, '❓ Sconosciuta')

            fig.update_layout(
                title="%s<br><sub>Fonte: %s | %s record</sub>" % (chart_title, source_display, len(df))
            )

        except Exception as chart_error:
            sys.stdout.write("❌ Errore generazione grafico: %s\n" % str(chart_error))
            sys.stdout.flush()
            return jsonify({
                'error': 'Errore nella generazione del grafico: %s' % str(chart_error),
                'chart_type': chart_type,
                'data_available': len(df) > 0
            })

        # Serializza e restituisci il grafico
        import plotly.utils  # pylint: disable=import-outside-toplevel
        chart_json = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)

        # Calcola tempo di esecuzione
        end_time = time.time()
        execution_time = end_time - start_time

        sys.stdout.write("✅ Grafico %s generato in %.2fs\n" % (chart_type, execution_time))
        sys.stdout.write("📊 JSON: %s bytes\n" % len(chart_json))
        sys.stdout.flush()

        return jsonify({
            'chart': chart_json,
            'chart_type': chart_type,
            'data_source': data_source_used,
            'record_count': len(df),
            'execution_time': execution_time,
            'generation_info': {
                'chart_type': chart_type,
                'subtype': chart_subtype,
                'source': data_source_used
            }
        })

    except Exception as e:
        sys.stdout.write("❌ Errore critico endpoint: %s\n" % str(e))
        sys.stdout.flush()
        return jsonify({
            'error': 'Errore critico: %s' % str(e),
            'chart_type': chart_type
        })


# FUNZIONI HELPER PER DASHBOARD MODERNA

def calculate_modern_stats(data, file_type):
    """
    Calcola statistiche moderne per la dashboard.
    Supporta dati da Supabase e sessione.
    """
    try:
        if not data or len(data) == 0:
            return {}

        # Converti in DataFrame per analisi
        df = pd.DataFrame(data)

        stats = {
            'total_records': len(df),
            'data_quality_score': 95,  # Default alto
            'last_updated': datetime.now().isoformat()
        }

        # Statistiche specifiche per tipo file
        if file_type in ['teamviewer', 'attivita', 'normalized']:
            # Statistiche sessioni/attività
            stats['total_sessions'] = len(df)

            # Durata media
            duration_cols = [col for col in df.columns if 'durata' in col.lower() or 'duration' in col.lower()]
            if duration_cols:
                duration_col = duration_cols[0]
                if pd.api.types.is_numeric_dtype(df[duration_col]):
                    stats['avg_duration'] = df[duration_col].mean()
                    stats['total_duration'] = df[duration_col].sum()
                    stats['total_hours'] = stats['total_duration'] / 60 if 'minuti' in duration_col.lower() else stats['total_duration']

            # Tecnici/dipendenti unici
            tech_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in ['tecnico', 'dipendente', 'user', 'creato', 'assegnatario'])]
            if tech_cols:
                tech_col = tech_cols[0]
                stats['unique_technicians'] = df[tech_col].nunique()
                stats['technicians_count'] = stats['unique_technicians']

            # Clienti unici
            client_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in ['cliente', 'azienda', 'company', 'nome'])]
            if client_cols:
                client_col = client_cols[0]
                stats['unique_clients'] = df[client_col].nunique()
                stats['clients_count'] = stats['unique_clients']

        elif file_type in ['timbrature', 'presenze']:
            # Statistiche timbrature
            stats['total_events'] = len(df)

            # Ore lavorate
            hours_cols = [col for col in df.columns if 'ore' in col.lower() or 'hours' in col.lower()]
            if hours_cols:
                hours_col = hours_cols[0]
                if pd.api.types.is_numeric_dtype(df[hours_col]):
                    stats['total_hours'] = df[hours_col].sum()
                    stats['avg_duration'] = df[hours_col].mean() * 60  # Converti in minuti

        elif file_type in ['permessi', 'ferie']:
            # Statistiche permessi
            stats['total_events'] = len(df)

            # Dipendenti con permessi
            emp_cols = [col for col in df.columns if 'dipendente' in col.lower()]
            if emp_cols:
                emp_col = emp_cols[0]
                stats['unique_technicians'] = df[emp_col].nunique()

        # Calcola metriche di efficienza
        if 'total_hours' in stats and 'unique_technicians' in stats and stats['unique_technicians'] > 0:
            stats['efficiency'] = stats['total_hours'] / stats['unique_technicians']
            stats['workload'] = stats['total_records'] / stats['unique_technicians']

        # Assicura valori di default
        stats.setdefault('total_sessions', stats.get('total_records', 0))
        stats.setdefault('total_events', stats.get('total_records', 0))
        stats.setdefault('avg_duration', 0)
        stats.setdefault('total_hours', 0)
        stats.setdefault('unique_technicians', 0)
        stats.setdefault('technicians_count', 0)
        stats.setdefault('unique_clients', 0)
        stats.setdefault('clients_count', 0)
        stats.setdefault('efficiency', 1.0)
        stats.setdefault('workload', 0)

        sys.stdout.write("✅ Statistiche calcolate: %s metriche\n" % len(stats))
        sys.stdout.flush()

        return stats

    except Exception as e:
        sys.stdout.write("⚠️ Errore calcolo statistiche: %s\n" % str(e))
        sys.stdout.flush()
        return {
            'total_records': len(data) if data else 0,
            'total_sessions': len(data) if data else 0,
            'total_events': len(data) if data else 0,
            'avg_duration': 0,
            'total_hours': 0,
            'unique_technicians': 0,
            'technicians_count': 0,
            'unique_clients': 0,
            'clients_count': 0,
            'efficiency': 1.0,
            'workload': 0,
            'data_quality_score': 50,
            'last_updated': datetime.now().isoformat()
        }

# FUNZIONI HELPER PER GENERAZIONE GRAFICI MODERNE
def generate_time_series_chart(dataframe, chart_data_source):  # pylint: disable=unused-argument
    """Genera grafico temporale intelligente."""

    # Cerca colonne temporali
    date_columns = []
    for col in dataframe.columns:
        if any(keyword in col.lower() for keyword in ['data', 'date', 'inizio', 'iniziata']):
            date_columns.append(col)

    if not date_columns:
        # Fallback: usa indice come tempo
        df_temp = dataframe.copy()
        df_temp['index_time'] = range(len(df_temp))
        return px.line(df_temp, x='index_time', title='Trend Temporale (Indice)')

    # Usa la prima colonna data trovata
    date_col = date_columns[0]

    # Converti in datetime se necessario
    if not pd.api.types.is_datetime64_any_dtype(dataframe[date_col]):
        dataframe[date_col] = pd.to_datetime(dataframe[date_col], errors='coerce')

    # Aggrega per data se ci sono troppe righe
    if len(dataframe) > 100:
        df_agg = dataframe.groupby(dataframe[date_col].dt.date).size().reset_index(name='count')
        return px.line(df_agg, x=date_col, y='count', title='Attività nel Tempo')
    else:
        return px.line(dataframe, x=date_col, title='Timeline Attività')


def generate_distribution_chart(dataframe, chart_subtype, chart_data_source):  # pylint: disable=unused-argument
    """Genera grafico di distribuzione intelligente."""

    # Cerca colonne categoriche interessanti
    categorical_columns = []
    for col in dataframe.columns:
        if dataframe[col].dtype == 'object' and dataframe[col].nunique() < 20:
            categorical_columns.append(col)

    if not categorical_columns:
        return px.bar(x=['Nessun dato'], y=[1], title='Nessuna categoria disponibile')

    # Usa la prima colonna categorica
    cat_col = categorical_columns[0]

    # Limita a top 10 per performance
    top_values = dataframe[cat_col].value_counts().head(10)

    return px.bar(x=top_values.index, y=top_values.values,
                  title=f'Distribuzione {cat_col.title()}')


def generate_technician_chart(dataframe, chart_data_source):  # pylint: disable=unused-argument
    """Genera grafico tecnici intelligente."""

    # Cerca colonne tecnici
    tech_columns = []
    for col in dataframe.columns:
        if any(keyword in col.lower() for keyword in ['tecnico', 'dipendente', 'user', 'creato', 'assegnatario']):
            tech_columns.append(col)

    if not tech_columns:
        return px.bar(x=['Nessun tecnico'], y=[1], title='Nessun dato tecnici disponibile')

    tech_col = tech_columns[0]
    tech_counts = dataframe[tech_col].value_counts().head(10)

    return px.bar(x=tech_counts.index, y=tech_counts.values,
                  title=f'Attività per {tech_col.title()}')


def generate_client_chart(dataframe, chart_data_source):  # pylint: disable=unused-argument
    """Genera grafico clienti intelligente."""

    # Cerca colonne clienti
    client_columns = []
    for col in dataframe.columns:
        if any(keyword in col.lower() for keyword in ['cliente', 'client', 'azienda', 'company']):
            client_columns.append(col)

    if not client_columns:
        return px.bar(x=['Nessun cliente'], y=[1], title='Nessun dato clienti disponibile')

    client_col = client_columns[0]
    client_counts = dataframe[client_col].value_counts().head(10)

    return px.bar(x=client_counts.index, y=client_counts.values,
                  title=f'Attività per {client_col.title()}')


def generate_heatmap_chart(dataframe, chart_data_source):  # pylint: disable=unused-argument
    """Genera heatmap intelligente."""

    # Cerca colonne numeriche per correlazione
    numeric_columns = dataframe.select_dtypes(include=[np.number]).columns

    if len(numeric_columns) < 2:
        return px.bar(x=['Dati insufficienti'], y=[1], title='Dati numerici insufficienti per heatmap')

    # Calcola matrice di correlazione
    corr_matrix = dataframe[numeric_columns].corr()

    return px.imshow(corr_matrix, title='Heatmap Correlazioni',
                     color_continuous_scale='RdBu_r')


def generate_duration_chart(dataframe, chart_data_source):  # pylint: disable=unused-argument
    """Genera grafico durate intelligente."""

    # Cerca colonne durata
    duration_columns = []
    for col in dataframe.columns:
        if any(keyword in col.lower() for keyword in ['durata', 'duration', 'tempo', 'time']):
            duration_columns.append(col)

    if not duration_columns:
        return px.bar(x=['Nessuna durata'], y=[1], title='Nessun dato durata disponibile')

    duration_col = duration_columns[0]

    # Crea bins per durata
    if pd.api.types.is_numeric_dtype(dataframe[duration_col]):
        return px.histogram(dataframe, x=duration_col, title=f'Distribuzione {duration_col.title()}')
    else:
        return px.bar(x=['Formato non numerico'], y=[1], title='Durata non in formato numerico')


def generate_fallback_chart(dataframe, fallback_chart_type, chart_data_source):  # pylint: disable=unused-argument
    """Genera grafico fallback generico."""

    # Grafico semplice con prime colonne disponibili
    if len(dataframe.columns) == 0:
        return px.bar(x=['Nessun dato'], y=[1], title='Nessun dato disponibile')

    # Usa prima colonna come x
    x_col = dataframe.columns[0]

    if dataframe[x_col].dtype == 'object':
        # Categorica: conta valori
        counts = dataframe[x_col].value_counts().head(10)
        return px.bar(x=counts.index, y=counts.values, title=f'Distribuzione {x_col}')
    else:
        # Numerica: istogramma
        return px.histogram(dataframe, x=x_col, title=f'Distribuzione {x_col}')


@app.route('/test', methods=['GET'])
def test_upload_form():
    return render_template('test_upload.html')

@app.route('/test_upload', methods=['POST'])
def test_upload():
    sys.stdout.write("Inizio funzione test_upload\n")
    sys.stdout.flush()

    # Debug: stampa tutte le informazioni sulla richiesta
    sys.stdout.write(f"Request method: {request.method}\n")
    sys.stdout.write(f"Request form: {request.form}\n")
    sys.stdout.write(f"Request files: {request.files}\n")
    sys.stdout.flush()

    try:
        # Verifica se ci sono file nella richiesta
        if 'file' not in request.files:
            return "Errore: Nessun file selezionato"

        file = request.files['file']

        # Verifica se il nome del file è vuoto
        if file.filename == '':
            return "Errore: Nome file vuoto"

        # Salva il file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        return f"File caricato con successo: {filename}"

    except Exception as e:
        sys.stdout.write(f"Errore: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()
        return f"Errore: {str(e)}"

# Route per esportare i dati dal server MCP
@app.route('/export/mcp/<file_id>/<format>', methods=['GET'])
def export_mcp(file_id, format):
    """
    Esporta i dati elaborati dal server MCP.

    Args:
        file_id: ID del file da esportare
        format: Formato di esportazione (csv, excel, json)

    Returns:
        Reindirizzamento al download del file esportato
    """
    sys.stdout.write(f"=== ESPORTAZIONE DATI MCP IN {format.upper()} ===\n")
    sys.stdout.flush()

    try:
        # Verifica che il formato sia supportato
        if format not in ["csv", "excel", "json"]:
            flash(f'Formato non supportato: {format}', 'error')
            return redirect(url_for('dashboard'))

        # Costruisci l'URL per l'endpoint di esportazione del server MCP
        export_url = f"{mcp_url}/export/{file_id}/{format}"

        # Effettua la richiesta al server MCP
        import requests
        response = requests.get(export_url, stream=True)

        # Verifica la risposta
        if response.status_code != 200:
            flash(f'Errore nell\'esportazione dei dati: {response.text}', 'error')
            return redirect(url_for('dashboard'))

        # Crea un nome file per il download
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        download_filename = f"export_{file_id}_{timestamp}.{format}"
        download_path = os.path.join(app.config['UPLOAD_FOLDER'], download_filename)

        # Salva il file
        with open(download_path, 'wb') as f:  # pylint: disable=unspecified-encoding
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        sys.stdout.write(f"File {format.upper()} creato: {download_path}\n")
        sys.stdout.flush()

        # Reindirizza al download
        return redirect(url_for('download_file', filename=download_filename))

    except Exception as e:
        sys.stdout.write(f"Errore nell'esportazione dei dati MCP: {str(e)}\n")
        import traceback
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()
        flash(f'Errore nell\'esportazione dei dati: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# Route per esportare i dati in Excel
@app.route('/export/excel', methods=['GET', 'POST'])
def export_excel():
    import sys
    import os
    import pandas as pd
    import uuid
    from datetime import datetime

    sys.stdout.write("=== ESPORTAZIONE DATI IN EXCEL ===\n")
    sys.stdout.flush()

    # Gestione diversa in base al metodo HTTP
    if request.method == 'POST':
        try:
            # Ottieni i dati dalla richiesta POST
            data = request.json
            if not data:
                data = json.loads(request.form.get('data', '[]'))

            if not data:
                flash('Nessun dato da esportare', 'error')
                return redirect(url_for('dashboard'))

            # Crea un DataFrame
            df = pd.DataFrame(data)

            # Crea un nome file univoco
            filename = f"export_{uuid.uuid4().hex}.xlsx"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

            # Esporta in Excel
            df.to_excel(file_path, index=False)

            sys.stdout.write(f"File Excel creato: {file_path}\n")
            sys.stdout.flush()

            # Reindirizza al download
            return redirect(url_for('download_file', filename=filename))

        except Exception as e:
            sys.stdout.write(f"Errore nell'esportazione in Excel (POST): {str(e)}\n")
            sys.stdout.flush()
            flash(f'Errore nell\'esportazione in Excel: {str(e)}', 'error')
            return redirect(url_for('dashboard'))
    else:
        # Metodo GET - Esportazione dalla dashboard
        sys.stdout.write("Richiesta di esportazione in Excel (GET)\n")
        sys.stdout.flush()

        if 'processed_data' not in session and 'preview_data' not in session:
            flash('Nessun dato da esportare. Carica prima un file.', 'error')
            return redirect(url_for('dashboard'))

        # Usa i dati elaborati se disponibili, altrimenti usa i dati di anteprima
        data = session.get('processed_data', session.get('preview_data', []))
        filename = session.get('original_filename', session.get('filename', 'export'))
        file_type_var = session.get('file_type', 'generico')  # Rinominato per evitare conflitti

        sys.stdout.write(f"Export Excel - Nome file originale: {session.get('original_filename', 'non disponibile')}\n")
        sys.stdout.write(f"Export Excel - Nome file univoco: {session.get('filename', 'non disponibile')}\n")
        sys.stdout.write(f"Export Excel - Tipo file: {file_type_var}\n")
        sys.stdout.flush()

        try:
            # Crea un DataFrame dai dati
            df = pd.DataFrame(data)

            # Genera un nome file per l'esportazione
            base_name = os.path.splitext(filename)[0]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_filename = f"{base_name}_export_{timestamp}.xlsx"
            export_path = os.path.join(app.config['UPLOAD_FOLDER'], export_filename)

            sys.stdout.write(f"Esportazione in Excel: {export_path}\n")
            sys.stdout.flush()

            # Esporta in Excel usando pandas
            df.to_excel(export_path, index=False)

            sys.stdout.write(f"File Excel creato con successo: {export_path}\n")
            sys.stdout.flush()

            # Reindirizza al download
            return redirect(url_for('download_file', filename=export_filename))

        except Exception as e:
            sys.stdout.write(f"Errore nell'esportazione in Excel (GET): {str(e)}\n")
            sys.stdout.flush()
            flash(f'Errore nell\'esportazione in Excel: {str(e)}', 'error')
            return redirect(url_for('dashboard'))

# ===============================================
# DASHBOARD INTELLIGENTE - FASE 3
# ===============================================

@app.route('/intelligent-dashboard')
def intelligent_dashboard():
    """
    Dashboard intelligente con sistema di analisi incrociata.
    """
    return render_template('intelligent_dashboard.html')

@app.route('/configuration')
def configuration():
    """
    Pagina di configurazione sistema per gestione dipendenti, veicoli e automazione.
    """
    return render_template('configuration.html')

# Route rimossa - duplicata con quella alla riga 4723

@app.route('/api/intelligent-system/status')
def intelligent_system_status():
    """
    API per ottenere lo stato del sistema intelligente.
    """
    try:
        from intelligent_system_integration import get_intelligent_system  # pylint: disable=import-outside-toplevel

        # Usa la funzione factory per ottenere l'istanza inizializzata
        intelligent_system = get_intelligent_system()
        status = intelligent_system.get_system_status()

        return jsonify({
            'success': True,
            'status': status.__dict__ if hasattr(status, '__dict__') else status
        })

    except Exception as e:
        logger.error("Errore stato sistema intelligente: %s", str(e))
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def detect_file_type_from_preview(preview_data):
    """
    Rileva il tipo di file dai dati di preview.
    """
    if not preview_data:
        return 'generico'

    # Controlla le colonne per determinare il tipo
    columns = list(preview_data[0].keys()) if preview_data else []
    columns_lower = [col.lower() for col in columns]

    if any('dipendente' in col for col in columns_lower):
        return 'timbrature'
    elif any('targa' in col or 'veicolo' in col for col in columns_lower):
        return 'veicoli'
    elif any('teamviewer' in col or 'assegnatario' in col for col in columns_lower):
        return 'teamviewer'
    elif any('evento' in col or 'calendario' in col for col in columns_lower):
        return 'calendario'
    else:
        return 'generico'

def analyze_wizard_setup_data(preview_data_list=None):
    """
    Analizza i dati caricati nel wizard e genera suggerimenti intelligenti.
    """
    try:
        # Debug: stampa le chiavi della sessione
        sys.stdout.write(f"🔍 DEBUG: Chiavi sessione disponibili: {list(session.keys())}\n")
        sys.stdout.flush()

        # Usa i dati passati come parametro o quelli in sessione
        if preview_data_list:
            sys.stdout.write(f"✅ DEBUG: Usando dati passati come parametro ({len(preview_data_list)} file)\n")
            sys.stdout.flush()

            # Usa il primo file per determinare il tipo
            preview_data = preview_data_list[0] if preview_data_list else []
            file_type = detect_file_type_from_preview(preview_data)

        else:
            # Ottieni i dati dalla sessione (fallback)
            if 'preview_data' not in session or 'file_type' not in session:
                sys.stdout.write(f"❌ DEBUG: Dati mancanti in sessione - preview_data: {'preview_data' in session}, file_type: {'file_type' in session}\n")
                sys.stdout.flush()
                return {
                    'employees': [],
                    'vehicles': [],
                    'suggested_analysis': [],
                    'suggested_automations': []
                }

            preview_data = session.get('preview_data', [])
            file_type = session.get('file_type', 'unknown')

        # Analisi basata sul tipo di file
        employees = []
        vehicles = []
        suggested_analysis = []
        suggested_automations = []

        if file_type == 'timbrature':
            # Debug: stampa le chiavi disponibili
            if preview_data:
                sys.stdout.write(f"🔍 DEBUG: Chiavi disponibili nel primo record: {list(preview_data[0].keys())}\n")
                sys.stdout.flush()

            # Estrai dipendenti dalle timbrature
            for row in preview_data:
                # Prova diverse varianti di nomi colonne
                nome = None

                # Cerca la colonna "Dipendente" (case-insensitive)
                for key, value in row.items():
                    if key.lower() == 'dipendente' and value:
                        nome = str(value).strip()
                        break

                # Se non trovato, cerca colonne che contengono "dipendente"
                if not nome:
                    for key, value in row.items():
                        if 'dipendente' in key.lower() and value:
                            nome = str(value).strip()
                            break

                # Aggiungi se valido e non duplicato
                if nome and nome not in employees:
                    employees.append(nome)
                    sys.stdout.write(f"✅ DEBUG: Dipendente trovato: '{nome}'\n")
                    sys.stdout.flush()

            suggested_analysis = [
                {
                    'name': 'Analisi Ore Lavorate',
                    'description': 'Monitora le ore lavorate per dipendente e identifica anomalie'
                },
                {
                    'name': 'Analisi Produttività',
                    'description': 'Valuta l\'efficienza e la produttività del team'
                }
            ]

            suggested_automations = [
                {
                    'name': 'Report Ore Settimanali',
                    'description': 'Genera automaticamente report settimanali delle ore lavorate'
                },
                {
                    'name': 'Alert Straordinari',
                    'description': 'Notifica quando un dipendente supera le ore standard'
                }
            ]

        elif file_type == 'teamviewer':
            # Estrai tecnici dalle sessioni TeamViewer
            for row in preview_data:
                if 'Assegnatario' in row:
                    nome = row.get('Assegnatario', '')
                    if nome and nome not in employees:
                        employees.append(nome)

            suggested_analysis = [
                {
                    'name': 'Analisi Sessioni Remote',
                    'description': 'Monitora durata e frequenza delle sessioni di controllo remoto'
                },
                {
                    'name': 'Analisi Clienti',
                    'description': 'Identifica i clienti più assistiti e i pattern di supporto'
                }
            ]

            suggested_automations = [
                {
                    'name': 'Report Assistenza Mensile',
                    'description': 'Genera report mensili delle attività di assistenza'
                },
                {
                    'name': 'Alert Sessioni Lunghe',
                    'description': 'Notifica sessioni di assistenza superiori a 2 ore'
                }
            ]

        elif file_type == 'permessi':
            # Estrai dipendenti dalle richieste di permesso
            for row in preview_data:
                if 'Dipendente' in row:
                    nome = row.get('Dipendente', '')
                    if nome and nome not in employees:
                        employees.append(nome)

            suggested_analysis = [
                {
                    'name': 'Analisi Permessi',
                    'description': 'Monitora le richieste di permesso e identifica pattern'
                },
                {
                    'name': 'Analisi Assenze',
                    'description': 'Valuta l\'impatto delle assenze sulla produttività'
                }
            ]

            suggested_automations = [
                {
                    'name': 'Approvazione Automatica',
                    'description': 'Approva automaticamente permessi standard entro limiti predefiniti'
                },
                {
                    'name': 'Report Assenze Mensile',
                    'description': 'Genera report mensili delle assenze per dipendente'
                }
            ]

        elif file_type == 'registro_auto':
            # Estrai dipendenti e veicoli dal registro auto
            for row in preview_data:
                if 'Dipendente' in row:
                    nome = row.get('Dipendente', '')
                    if nome and nome not in employees:
                        employees.append(nome)
                if 'Auto' in row:
                    auto = row.get('Auto', '')
                    if auto and auto not in vehicles:
                        vehicles.append(auto)

            suggested_analysis = [
                {
                    'name': 'Analisi Utilizzo Veicoli',
                    'description': 'Monitora l\'utilizzo dei veicoli aziendali'
                },
                {
                    'name': 'Analisi Chilometraggio',
                    'description': 'Traccia i chilometri percorsi per veicolo e dipendente'
                }
            ]

            suggested_automations = [
                {
                    'name': 'Manutenzione Programmata',
                    'description': 'Programma automaticamente la manutenzione dei veicoli'
                },
                {
                    'name': 'Report Utilizzo Mensile',
                    'description': 'Genera report mensili dell\'utilizzo dei veicoli'
                }
            ]

        elif file_type == 'calendario':
            # Estrai partecipanti dagli eventi
            for row in preview_data:
                if 'Partecipanti' in row:
                    partecipanti = row.get('Partecipanti', '')
                    if partecipanti:
                        # Dividi i partecipanti se sono separati da virgole
                        for nome in partecipanti.split(','):
                            nome = nome.strip()
                            if nome and nome not in employees:
                                employees.append(nome)

            suggested_analysis = [
                {
                    'name': 'Analisi Calendario',
                    'description': 'Monitora gli eventi e la partecipazione del team'
                },
                {
                    'name': 'Analisi Carico Lavoro',
                    'description': 'Valuta il carico di lavoro basato sugli eventi programmati'
                }
            ]

            suggested_automations = [
                {
                    'name': 'Sincronizzazione Calendari',
                    'description': 'Sincronizza automaticamente i calendari del team'
                },
                {
                    'name': 'Promemoria Automatici',
                    'description': 'Invia promemoria automatici per eventi importanti'
                }
            ]

        else:
            # Tipo generico - analisi base
            suggested_analysis = [
                {
                    'name': 'Analisi Dati Generica',
                    'description': 'Analisi esplorativa dei dati caricati'
                }
            ]

            suggested_automations = [
                {
                    'name': 'Backup Automatico',
                    'description': 'Backup automatico dei dati caricati'
                }
            ]

        result = {
            'employees': employees[:10],  # Limita a 10 per performance
            'vehicles': vehicles[:10],    # Limita a 10 per performance
            'suggested_analysis': suggested_analysis,
            'suggested_automations': suggested_automations
        }

        # Debug: stampa il risultato
        sys.stdout.write(f"✅ DEBUG: Analisi completata - Dipendenti: {len(result['employees'])}, Veicoli: {len(result['vehicles'])}, Analisi: {len(result['suggested_analysis'])}, Automazioni: {len(result['suggested_automations'])}\n")
        sys.stdout.flush()

        return result

    except Exception as e:
        sys.stdout.write(f"Errore analisi wizard setup: {str(e)}\n")
        sys.stdout.flush()
        return {
            'employees': [],
            'vehicles': [],
            'suggested_analysis': [],
            'suggested_automations': []
        }

# RIMOSSO: Primo blocco if __name__ == '__main__' che avviava Hypercorn
# Questo causava il problema 404 delle route Flask
# Il blocco di avvio corretto è alla fine del file

    # # Per debug e sviluppo, usa Flask standard
    # if os.environ.get('FLASK_DEBUG', '0') == '1':
    #     print("🚀 Avvio in modalità debug con Flask standard")
    #     app.run(host='0.0.0.0' if in_docker else '127.0.0.1', port=5000, debug=True)
    # else:
    #     # Per produzione, usa Hypercorn
    #     print("🚀 Avvio in modalità produzione con Hypercorn")

        # # Supporto per le chiamate asincrone con gestore Lifespan personalizzato
        # class LifespanMiddleware:
        #     def __init__(self, app):
        #         self.app = app

        #     async def __call__(self, scope, receive, send):
        #         if scope["type"] == "lifespan":
        #             # Gestisci gli eventi lifespan qui
        #             while True:
        #                 message = await receive()
        #                 if message["type"] == "lifespan.startup":
        #                     # Esegui operazioni di startup se necessario
        #                     await send({"type": "lifespan.startup.complete"})
        #                 elif message["type"] == "lifespan.shutdown":
        #                     # Esegui operazioni di shutdown se necessario
        #                     await send({"type": "lifespan.shutdown.complete"})
        #                     break
        #         else:
        #             # Per tutti gli altri tipi di scope (http, websocket), passa al WSGI app
        #             await self.app(scope, receive, send)

        # # Converti l'app Flask in ASGI e aggiungi il middleware Lifespan
        # asgi_app = LifespanMiddleware(WsgiToAsgi(app))

        # # Configurazione di Hypercorn
        # config = HypercornConfig()
        # config.bind = ["0.0.0.0:5000" if in_docker else "127.0.0.1:5000"]
        # config.use_reloader = True

        # # Disabilita i log di accesso per ridurre il rumore
        # config.accesslog = None

        # # Avvia l'applicazione con Hypercorn
        # asyncio.run(serve(asgi_app, config))

# Route di test per l'upload dei file (DUPLICATE REMOVED)
# La route /test è già definita alla riga 3857
# La route /test_upload è già definita alla riga 3861
# La route /export/mcp/<file_id>/<format> è già definita alla riga 3900
# La route /export/excel è già definita alla riga 3960
# La route /download/<filename> è stata spostata alla riga 2320 per evitare errori Flask

# L'endpoint export_excel è stato unificato per supportare sia GET che POST

# API per esportare i dati in CSV
@app.route('/export/csv', methods=['GET', 'POST'])
def export_csv():
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== ESPORTAZIONE DATI IN CSV ===\n")
    sys.stdout.flush()

    try:
        # Gestione diversa in base al metodo HTTP
        if request.method == 'POST':
            # Ottieni i dati dalla richiesta POST
            data = request.json
            if not data:
                data = json.loads(request.form.get('data', '[]'))
        else:
            # Metodo GET - Esportazione dalla dashboard
            sys.stdout.write("Richiesta di esportazione in CSV (GET)\n")
            sys.stdout.flush()

            if 'processed_data' not in session and 'preview_data' not in session:
                flash('Nessun dato da esportare. Carica prima un file.', 'error')
                return redirect(url_for('dashboard'))

            # Usa i dati dalla sessione
            data = session.get('processed_data') or session.get('preview_data', [])

        if not data:
            flash('Nessun dato da esportare', 'error')
            return redirect(url_for('dashboard'))

        # Crea un DataFrame
        df = pd.DataFrame(data)

        # Crea un nome file univoco
        filename = f"export_{uuid.uuid4().hex}.csv"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Esporta in CSV
        df.to_csv(file_path, index=False)

        sys.stdout.write(f"File CSV creato: {file_path}\n")
        sys.stdout.flush()

        # Reindirizza al download
        return redirect(url_for('download_file', filename=filename))

    except Exception as e:
        sys.stdout.write(f"Errore nell'esportazione in CSV: {str(e)}\n")
        sys.stdout.flush()
        flash(f'Errore nell\'esportazione in CSV: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# API per esportare i dati in JSON
@app.route('/export/json', methods=['POST'])
def export_json():
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== ESPORTAZIONE DATI IN JSON ===\n")
    sys.stdout.flush()

    try:
        # Ottieni i dati dalla richiesta
        data = request.json
        if not data:
            data = json.loads(request.form.get('data', '[]'))

        if not data:
            flash('Nessun dato da esportare', 'error')
            return redirect(url_for('dashboard'))

        # Crea un nome file univoco
        filename = f"export_{uuid.uuid4().hex}.json"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Esporta in JSON
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, cls=CustomJSONEncoder, indent=2)

        sys.stdout.write(f"File JSON creato: {file_path}\n")
        sys.stdout.flush()

        # Reindirizza al download
        return redirect(url_for('download_file', filename=filename))

    except Exception as e:
        sys.stdout.write(f"Errore nell'esportazione in JSON: {str(e)}\n")
        sys.stdout.flush()
        flash(f'Errore nell\'esportazione in JSON: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# API per esportare i dati in PDF
@app.route('/export/pdf', methods=['GET', 'POST'])
def export_pdf():
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== ESPORTAZIONE DATI IN PDF ===\n")
    sys.stdout.flush()

    try:
        # Gestione diversa in base al metodo HTTP
        if request.method == 'POST':
            # Ottieni i dati dalla richiesta POST
            data = request.json
            if not data:
                data = json.loads(request.form.get('data', '[]'))
        else:
            # Metodo GET - Esportazione dalla dashboard
            sys.stdout.write("Richiesta di esportazione in PDF (GET)\n")
            sys.stdout.flush()

            if 'processed_data' not in session and 'preview_data' not in session:
                flash('Nessun dato da esportare. Carica prima un file.', 'error')
                return redirect(url_for('dashboard'))

            # Usa i dati dalla sessione
            data = session.get('processed_data') or session.get('preview_data', [])

        if not data:
            flash('Nessun dato da esportare', 'error')
            return redirect(url_for('dashboard'))

        # Crea un nome file univoco
        filename = f"export_{uuid.uuid4().hex}.pdf"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Usa il PDF generator esistente
        from pdf_generator import PDFGenerator  # pylint: disable=import-outside-toplevel

        pdf_gen = PDFGenerator()
        pdf_gen.generate_data_report(data, file_path)

        sys.stdout.write(f"File PDF creato: {file_path}\n")
        sys.stdout.flush()

        # Reindirizza al download
        return redirect(url_for('download_file', filename=filename))

    except Exception as e:
        sys.stdout.write(f"Errore nell'esportazione in PDF: {str(e)}\n")
        sys.stdout.flush()
        flash(f'Errore nell\'esportazione in PDF: {str(e)}', 'error')
        return redirect(url_for('dashboard'))



# Funzione per pulire la cache e i file temporanei obsoleti all'avvio
def clean_on_startup():
    # Import già presenti nella sezione top-level

    sys.stdout.write("=== PULIZIA ALL'AVVIO DELL'APPLICAZIONE ===\n")

    # Pulisci la cache dei grafici
    global chart_cache
    chart_cache = {}

    # Pulisci i file temporanei obsoleti (più vecchi di 7 giorni)
    temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp')
    if os.path.exists(temp_dir):
        try:
            sys.stdout.write(f"Pulizia file temporanei obsoleti in: {temp_dir}\n")

            # Calcola la data limite (7 giorni fa)
            now = datetime.now()
            limit_date = now - timedelta(days=7)

            # Conta i file rimossi
            removed_count = 0

            # Itera sui file nella directory temporanea
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)

                # Ottieni la data di ultima modifica
                try:
                    mtime = os.path.getmtime(file_path)
                    file_date = datetime.fromtimestamp(mtime)

                    # Se il file è più vecchio del limite, rimuovilo
                    if file_date < limit_date:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                        removed_count += 1
                except Exception as e:
                    sys.stdout.write(f"Errore nella rimozione del file {file_path}: {str(e)}\n")

            sys.stdout.write(f"Rimossi {removed_count} file temporanei obsoleti\n")
        except Exception as e:
            sys.stdout.write(f"Errore nella pulizia dei file temporanei: {str(e)}\n")
    else:
        # Crea la directory temporanea se non esiste
        try:
            os.makedirs(temp_dir, exist_ok=True)
            sys.stdout.write(f"Directory temporanea creata: {temp_dir}\n")
        except Exception as e:
            sys.stdout.write(f"Errore nella creazione della directory temporanea: {str(e)}\n")

    # Crea una cartella temporanea per i file caricati
    temp_upload_folder = os.path.join(os.path.dirname(app.config['UPLOAD_FOLDER']), 'uploads_temp')

    try:
        # Crea la cartella temporanea se non esiste
        os.makedirs(temp_upload_folder, exist_ok=True)

        # Sposta i file necessari nella cartella temporanea
        # Aggiungiamo calendario_190525.csv all'elenco dei file da preservare
        files_to_preserve = [
            'calendario_080525.csv',
            'teamviewer_bait_190525.csv',
            'Controlli_quotidiani.xlsx',
            'timbrature_190525.xlsx'
        ]

        # Stampa tutti i file presenti nella cartella uploads
        sys.stdout.write("File presenti nella cartella uploads prima della pulizia:\n")
        if os.path.exists(app.config['UPLOAD_FOLDER']):
            for f in os.listdir(app.config['UPLOAD_FOLDER']):
                sys.stdout.write(f"- {f}\n")

        for filename in files_to_preserve:
            src_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            if os.path.exists(src_path):
                dst_path = os.path.join(temp_upload_folder, filename)
                shutil.copy2(src_path, dst_path)
                sys.stdout.write(f"File copiato: {filename}\n")

        # Rimuovi la cartella uploads e ricreala
        shutil.rmtree(app.config['UPLOAD_FOLDER'], ignore_errors=True)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

        # Ripristina i file necessari
        for filename in files_to_preserve:
            src_path = os.path.join(temp_upload_folder, filename)
            if os.path.exists(src_path):
                dst_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                shutil.copy2(src_path, dst_path)
                sys.stdout.write(f"File ripristinato: {filename}\n")

        # Stampa tutti i file presenti nella cartella uploads dopo la pulizia
        sys.stdout.write("File presenti nella cartella uploads dopo la pulizia:\n")
        if os.path.exists(app.config['UPLOAD_FOLDER']):
            for f in os.listdir(app.config['UPLOAD_FOLDER']):
                sys.stdout.write(f"- {f}\n")

        # Rimuovi la cartella temporanea
        shutil.rmtree(temp_upload_folder, ignore_errors=True)

        sys.stdout.write("Pulizia completata con successo\n")
    except Exception as e:
        sys.stdout.write(f"Errore durante la pulizia: {str(e)}\n")

    sys.stdout.flush()

# === ROUTE PER GESTIONE COSTI DIPENDENTI ===

@app.route('/employee-costs')
def employee_costs():
    """Pagina per gestire i costi dipendenti"""
    # Ottieni tutti i costi dipendenti
    employee_costs_data = config_manager.get_all_employee_costs()
    tax_settings = config_manager.get_tax_settings()

    return render_template('employee_costs.html',
                         employee_costs=employee_costs_data,
                         tax_settings=tax_settings)

@app.route('/api/employee-costs', methods=['GET'])
def api_get_employee_costs():
    """API per ottenere tutti i costi dipendenti"""
    try:
        employee_costs_data = config_manager.get_all_employee_costs()
        tax_settings = config_manager.get_tax_settings()

        return jsonify({
            'success': True,
            'employee_costs': employee_costs_data,
            'tax_settings': tax_settings
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/employee-costs', methods=['POST'])
def api_set_employee_cost():
    """API per impostare il costo di un dipendente"""
    try:
        data = request.get_json()

        employee_name = data.get('employee_name')
        hourly_rate = data.get('hourly_rate')
        vat_included = data.get('vat_included', True)
        notes = data.get('notes', '')

        if not employee_name or hourly_rate is None:
            return jsonify({
                'success': False,
                'error': 'Nome dipendente e costo orario sono obbligatori'
            }), 400

        success = config_manager.set_employee_cost(
            employee_name=employee_name,
            hourly_rate=float(hourly_rate),
            vat_included=bool(vat_included),
            notes=str(notes)
        )

        if success:
            return jsonify({
                'success': True,
                'message': f'Costo per {employee_name} salvato con successo'
            })

        return jsonify({
            'success': False,
            'error': 'Errore nel salvare il costo dipendente'
        }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/employee-costs/<employee_name>', methods=['DELETE'])
def api_delete_employee_cost(employee_name):
    """API per rimuovere il costo di un dipendente"""
    try:
        success = config_manager.remove_employee_cost(employee_name)

        if success:
            return jsonify({
                'success': True,
                'message': f'Costo per {employee_name} rimosso con successo'
            })

        return jsonify({
            'success': False,
            'error': 'Errore nella rimozione del costo dipendente'
        }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/tax-settings', methods=['POST'])
def api_set_tax_settings():
    """API per aggiornare le impostazioni IVA"""
    try:
        data = request.get_json()

        vat_rate = data.get('vat_rate')
        default_vat_included = data.get('default_vat_included')
        currency = data.get('currency')

        success = config_manager.set_tax_settings(
            vat_rate=vat_rate,
            default_vat_included=default_vat_included,
            currency=currency
        )

        if success:
            return jsonify({
                'success': True,
                'message': 'Impostazioni IVA aggiornate con successo'
            })

        return jsonify({
            'success': False,
            'error': 'Errore nell\'aggiornare le impostazioni IVA'
        }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/calculate-employee-cost', methods=['POST'])
def api_calculate_employee_cost():
    """API per calcolare il costo totale di un dipendente"""
    try:
        data = request.get_json()

        employee_name = data.get('employee_name')
        hours = data.get('hours')
        include_vat = data.get('include_vat')

        if not employee_name or hours is None:
            return jsonify({
                'success': False,
                'error': 'Nome dipendente e ore sono obbligatori'
            }), 400

        calculation_result = config_manager.calculate_employee_cost(
            employee_name=employee_name,
            hours=float(hours),
            include_vat=include_vat
        )

        if 'error' in calculation_result:
            return jsonify({
                'success': False,
                'error': calculation_result['error']
            }), 404

        return jsonify({
            'success': True,
            'calculation': calculation_result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/extract-employees', methods=['POST'])
def api_extract_employees():
    """API per estrarre dipendenti dai dati caricati"""
    try:
        # Verifica che ci siano dati in sessione
        if 'file_path' not in session:
            return jsonify({
                'success': False,
                'error': 'Nessun file caricato'
            }), 400

        file_path = session.get('file_path')

        # Leggi il file
        df, error = read_file(file_path)
        if error:
            return jsonify({
                'success': False,
                'error': f'Errore lettura file: {error}'
            }), 500

        # Estrai dipendenti
        employees = config_manager.get_employees_from_data(df)

        return jsonify({
            'success': True,
            'employees': employees
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== ROUTE AGENTI AI E AUTOMAZIONE - DUPLICATE RIMOSSE =====
# Le route sono ora definite anticipatamente alla riga 643-763

# Route duplicate rimosse - definite anticipatamente alle righe 680-763

@app.route('/api/automation/trigger', methods=['POST'])
def trigger_automation_event():
    """
    Attiva manualmente un evento di automazione.
    """
    if not AI_AGENTS_AVAILABLE:
        return jsonify({
            'success': False,
            'error': 'Sistema di automazione non disponibile'
        }), 503

    try:
        data = request.get_json()
        event_type = data.get('event_type')
        event_data = data.get('event_data', {})

        if not event_type:
            return jsonify({
                'success': False,
                'error': 'event_type richiesto'
            }), 400

        # Attiva l'evento in modo asincrono
        # Import già organizzati nella sezione top-level
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Assicurati che intelligent_automation sia definito
            if not hasattr(app, 'intelligent_automation') or app.intelligent_automation is None:
                # Carica sistemi lazy se non disponibili
                if hasattr(app, 'db_manager') and app.db_manager:
                    from intelligent_automation import IntelligentAutomation  # pylint: disable=import-outside-toplevel
                    app.intelligent_automation = IntelligentAutomation()
                else:
                    raise Exception("Database manager non disponibile per inizializzare intelligent automation")

            results = loop.run_until_complete(
                app.intelligent_automation.trigger_event(event_type, event_data)
            )
        finally:
            loop.close()

        return jsonify({
            'success': True,
            'triggered_rules': len(results),
            'results': results
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/wizard/test', methods=['GET', 'POST', 'OPTIONS'])
def test_wizard():
    """Endpoint di test per verificare la connettività"""
    # Import già organizzati nella sezione top-level
    sys.stdout.write("=== TEST WIZARD ENDPOINT ===\n")
    sys.stdout.write(f"🔍 Metodo: {request.method}\n")
    sys.stdout.flush()

    if request.method == 'OPTIONS':
        response = make_response()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response

    response_data = {
        'success': True,
        'message': 'Test endpoint funzionante!',
        'method': request.method,
        'timestamp': datetime.now().isoformat()
    }

    flask_response = make_response(jsonify(response_data), 200)
    flask_response.headers.add("Access-Control-Allow-Origin", "*")
    return flask_response

# Endpoint wizard rimosso - ora definito alla riga 2337

@app.route('/api/file/analyze_enhanced', methods=['POST'])
def analyze_file_enhanced():
    """
    Analizza un file usando l'Enhanced Real File Analyzer con agenti AI.
    """
    if not AI_AGENTS_AVAILABLE:
        return jsonify({
            'success': False,
            'error': 'Agenti AI non disponibili'
        }), 503

    try:
        # Ottieni il percorso del file dalla sessione o dai parametri
        file_path = session.get('file_path')
        if not file_path:
            data = request.get_json()
            file_path = data.get('file_path')

        if not file_path or not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': 'File non trovato'
            }), 400

        # Esegui analisi avanzata tramite agente
        task_data = {
            'file_path': file_path,
            'save_to_cloud': True
        }

        # Import già organizzati nella sezione top-level
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Assicurati che agent_orchestrator sia definito
            if not hasattr(app, 'agent_orchestrator') or app.agent_orchestrator is None:
                # Carica sistemi lazy se non disponibili
                if hasattr(app, 'db_manager') and app.db_manager:
                    from advanced_agent_framework import get_advanced_orchestrator  # pylint: disable=import-outside-toplevel
                    app.agent_orchestrator = get_advanced_orchestrator()  # Nessun parametro richiesto
                else:
                    raise Exception("Database manager non disponibile per inizializzare agent orchestrator")

            analysis_result = loop.run_until_complete(
                app.agent_orchestrator.execute_task('file_analysis', task_data)
            )
        finally:
            loop.close()

        # Attiva evento di automazione per file analizzato
        if analysis_result.get('success'):
            event_data = {
                'file_path': file_path,
                'analysis_result': analysis_result,
                'file_extension': os.path.splitext(file_path)[1].lower().replace('.', '')
            }

            # Trigger automazione in background (non aspettiamo il risultato)
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                # Assicurati che intelligent_automation sia definito
                if not hasattr(app, 'intelligent_automation') or app.intelligent_automation is None:
                    if hasattr(app, 'db_manager') and app.db_manager:
                        from intelligent_automation import IntelligentAutomation  # pylint: disable=import-outside-toplevel
                        app.intelligent_automation = IntelligentAutomation()

                if hasattr(app, 'intelligent_automation') and app.intelligent_automation:
                    loop.run_until_complete(
                        app.intelligent_automation.trigger_event('file_upload', event_data)
                    )
                loop.close()
            except Exception as automation_error:
                logger.warning("Errore automazione: %s", str(automation_error))

        return jsonify({
            'success': True,
            'analysis_result': analysis_result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===============================================
# DASHBOARD INTELLIGENTE - FASE 3 (DUPLICATE REMOVED)
# ===============================================
# La route /intelligent-dashboard è già definita alla riga 4055
# La route /configuration è già definita alla riga 4062
# La route /setup-wizard è già definita alla riga 2267

# Route /api/intelligent-system/status già definita alla riga 4071

# FUNZIONI DUPLICATE RIMOSSE - detect_file_type_from_preview e analyze_wizard_setup_data
# Le implementazioni originali si trovano alle righe 5617 e 5639

# ROUTE RIMOSSA - Spostata prima dell'avvio del server (riga 4072) per evitare problemi di registrazione
# La route è ora correttamente posizionata e registrata

# ROUTE RIMOSSA - Spostata prima dell'avvio del server (riga 4196) per evitare problemi di registrazione
# La route è ora correttamente posizionata e registrata

@app.route('/api/intelligent-system/analyze', methods=['POST'])
def intelligent_system_analyze():
    """
    API per eseguire analisi incrociata completa.
    Approccio Supabase-first con sistema intelligente integrato.
    """
    # Import già organizzati nella sezione top-level

    sys.stdout.write("=== API INTELLIGENT_SYSTEM_ANALYZE SUPABASE-FIRST ===\n")
    sys.stdout.flush()

    try:
        # Ottieni parametri richiesta
        data = request.get_json() or {}
        date_from = data.get('date_from')
        date_to = data.get('date_to')
        analysis_type = data.get('analysis_type', 'comprehensive')

        # Validazione parametri
        if not date_from or not date_to:
            # Usa date di default se non specificate
            date_to = datetime.now().strftime('%Y-%m-%d')
            date_from = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            sys.stdout.write(f"⚠️ Date non specificate, uso default: {date_from} - {date_to}\n")
            sys.stdout.flush()

        sys.stdout.write(f"📊 Analisi richiesta: {analysis_type} per periodo {date_from} - {date_to}\n")
        sys.stdout.flush()

        # STRATEGIA 1: Usa Intelligent System Integration (preferito)
        try:
            from intelligent_system_integration import get_intelligent_system  # pylint: disable=import-outside-toplevel

            sys.stdout.write("🚀 INTELLIGENT_SYSTEM: Utilizzo sistema integrato...\n")
            sys.stdout.flush()

            # Ottieni istanza sistema intelligente
            intelligent_system = get_intelligent_system()

            # Esegui analisi asincrona
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            analysis_results = loop.run_until_complete(
                intelligent_system.run_intelligent_analysis(date_from, date_to, analysis_type)
            )

            loop.close()

            sys.stdout.write(f"✅ INTELLIGENT_SYSTEM: Analisi completata con {len(analysis_results.get('components_used', []))} componenti\n")
            sys.stdout.flush()

            return jsonify({
                'success': True,
                'results': analysis_results,
                'analysis_type': analysis_type,
                'period': f"{date_from} - {date_to}",
                'system_used': 'intelligent_system_integration',
                'timestamp': datetime.now().isoformat()
            })

        except Exception as intelligent_error:
            sys.stdout.write(f"❌ INTELLIGENT_SYSTEM: Errore: {str(intelligent_error)}\n")
            sys.stdout.flush()

        # STRATEGIA 2: Usa Cross Analysis Engine (fallback)
        try:
            from cross_analysis_engine import CrossAnalysisEngine  # pylint: disable=import-outside-toplevel
            from advanced_database_manager import AdvancedDatabaseManager  # pylint: disable=import-outside-toplevel
            from supabase_integration import SupabaseManager  # pylint: disable=import-outside-toplevel

            sys.stdout.write("🔄 CROSS_ANALYSIS: Utilizzo motore analisi incrociata...\n")
            sys.stdout.flush()

            # Inizializza componenti
            supabase_manager = SupabaseManager()
            db_manager = AdvancedDatabaseManager(supabase_manager)
            analysis_engine = CrossAnalysisEngine(db_manager)

            # Inizializza results
            results = {}

            # Esegui analisi
            if analysis_type == 'comprehensive':
                results = analysis_engine.run_comprehensive_analysis(date_from, date_to)
            elif analysis_type in ['time_consistency', 'activity_remote_correlation', 'duplicates_overlaps',
                                 'productivity_analysis', 'cost_analysis', 'data_quality']:
                # Analisi specifica
                if analysis_type == 'time_consistency':
                    results = {'time_consistency': analysis_engine.analyze_time_consistency(date_from, date_to)}
                elif analysis_type == 'activity_remote_correlation':
                    results = {'activity_remote_correlation': analysis_engine.analyze_activity_remote_correlation(date_from, date_to)}
                elif analysis_type == 'duplicates_overlaps':
                    results = {'duplicates_overlaps': analysis_engine.analyze_duplicates_and_overlaps(date_from, date_to)}
                elif analysis_type == 'productivity_analysis':
                    results = {'productivity_analysis': analysis_engine.analyze_productivity(date_from, date_to)}
                elif analysis_type == 'cost_analysis':
                    results = {'cost_analysis': analysis_engine.analyze_costs_and_billing(date_from, date_to)}
                elif analysis_type == 'data_quality':
                    results = {'data_quality': analysis_engine.analyze_data_quality(date_from, date_to)}
            else:
                # Default: analisi completa
                results = analysis_engine.run_comprehensive_analysis(date_from, date_to)

            sys.stdout.write(f"✅ CROSS_ANALYSIS: Analisi {analysis_type} completata\n")
            sys.stdout.flush()

            return jsonify({
                'success': True,
                'results': results,
                'analysis_type': analysis_type,
                'period': f"{date_from} - {date_to}",
                'system_used': 'cross_analysis_engine',
                'timestamp': datetime.now().isoformat()
            })

        except Exception as cross_analysis_error:
            sys.stdout.write(f"❌ CROSS_ANALYSIS: Errore: {str(cross_analysis_error)}\n")
            sys.stdout.flush()

        # STRATEGIA 3: Analisi demo (ultimo fallback)
        sys.stdout.write("🎭 DEMO: Generazione analisi demo...\n")
        sys.stdout.flush()

        demo_results = {
            'analysis_type': analysis_type,
            'period': f"{date_from} - {date_to}",
            'timestamp': datetime.now().isoformat(),
            'components_used': ['demo_system'],
            'demo_analysis': {
                'total_records_analyzed': 150,
                'discrepancies_found': 3,
                'quality_score': 85.5,
                'recommendations': [
                    'Verificare sovrapposizioni temporali per Tecnico A',
                    'Controllare durate eccessive per Cliente X',
                    'Aggiornare informazioni mancanti per 5 record'
                ],
                'summary_stats': {
                    'technicians_analyzed': 5,
                    'clients_analyzed': 12,
                    'time_range_days': 30,
                    'data_completeness': 92.3
                }
            }
        }

        return jsonify({
            'success': True,
            'results': demo_results,
            'analysis_type': analysis_type,
            'period': f"{date_from} - {date_to}",
            'system_used': 'demo_fallback',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        # Import già organizzati nella sezione top-level
        sys.stdout.write(f"❌ Errore API intelligent-system/analyze: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        return jsonify({
            'success': False,
            'error': str(e),
            'analysis_type': analysis_type if 'analysis_type' in locals() else 'unknown',
            'system_used': 'error',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/intelligent-system/analytics')
def intelligent_system_analytics():
    """
    API per ottenere analytics del sistema intelligente.
    """
    try:
        from intelligent_system_integration import get_intelligent_system  # pylint: disable=import-outside-toplevel

        # Usa la funzione factory per ottenere l'istanza inizializzata
        intelligent_system = get_intelligent_system()

        # Ottieni parametri query
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        analytics = intelligent_system.get_processing_analytics(date_from, date_to)

        return jsonify({
            'success': True,
            'analytics': analytics
        })

    except Exception as e:
        logger.error("Errore analytics sistema intelligente: %s", str(e))
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== API SYSTEM STATUS =====

@app.route('/api/system-status')
def system_status():
    """
    API per ottenere lo stato completo del sistema.
    """
    try:
        from system_status_dashboard import SystemStatusDashboard  # pylint: disable=import-outside-toplevel

        # Crea dashboard di stato
        status_dashboard = SystemStatusDashboard()
        dashboard_status = status_dashboard.check_all_systems()

        # Aggiungi stato sistema intelligente
        try:
            from intelligent_system_integration import get_intelligent_system  # pylint: disable=import-outside-toplevel
            intelligent_system = get_intelligent_system()
            intelligent_status = intelligent_system.get_system_status()

            dashboard_status['intelligent_system'] = {
                'system_ready': intelligent_status.system_ready,
                'components': intelligent_status.components,
                'master_entities': intelligent_status.master_entities,
                'total_analyses': intelligent_status.total_analyses,
                'success_rate': intelligent_status.success_rate
            }
        except Exception as e:
            dashboard_status['intelligent_system'] = {'error': str(e)}

        return jsonify({
            'success': True,
            'system_status': dashboard_status
        })

    except Exception as e:
        logger.error("Errore stato sistema: %s", str(e))
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ===== API CONFIGURAZIONI =====

# ROUTE RIMOSSA - Sostituita da get_config_employees() (riga 6108) per evitare duplicazioni
# La nuova implementazione Supabase-first è più robusta e completa

# RIMOSSA: Route POST duplicata che causava conflitto con route GET anticipata
# La funzionalità POST è disponibile tramite /api/employee-costs
# @app.route('/api/config/employees', methods=['POST'])
def set_employee_config_removed():
    """RIMOSSA: Funzione duplicata che causava conflitto route."""
    return jsonify({'error': 'Funzione rimossa'}), 410

@app.route('/api/config/employees/<employee_name>', methods=['DELETE'])
def delete_employee_config(employee_name):
    """Rimuove la configurazione di un dipendente."""
    try:
        success = config_manager.remove_employee_cost(employee_name)

        if success:
            return jsonify({'success': True, 'message': f'Configurazione rimossa per {employee_name}'})
        return jsonify({'success': False, 'error': 'Dipendente non trovato'}), 404

    except Exception as e:
        logger.error("Errore nella rimozione configurazione dipendente: %s", str(e))
        return jsonify({'success': False, 'error': str(e)}), 500

# ROUTE RIMOSSA - Sostituita da get_config_vehicles() (riga 6248) per evitare duplicazioni
# La nuova implementazione Supabase-first è più robusta e completa

@app.route('/api/config/vehicles', methods=['POST'])
def set_vehicle_config():
    """Imposta la configurazione di un veicolo."""
    try:
        data = request.get_json()

        vehicle_name = data.get('vehicle_name')
        fuel_consumption = data.get('fuel_consumption')
        daily_cost = data.get('daily_cost')

        if not vehicle_name or fuel_consumption is None or daily_cost is None:
            return jsonify({'success': False, 'error': 'Nome veicolo, consumo e costo giornaliero sono obbligatori'}), 400

        success = config_manager.set_vehicle_cost(vehicle_name, fuel_consumption, daily_cost)

        if success:
            return jsonify({'success': True, 'message': f'Configurazione salvata per {vehicle_name}'})
        return jsonify({'success': False, 'error': 'Errore nel salvataggio'}), 500

    except Exception as e:
        logger.error("Errore nell'impostazione configurazione veicolo: %s", str(e))
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/config/tax', methods=['POST'])
def set_tax_config():
    """Imposta le configurazioni IVA."""
    try:
        data = request.get_json()

        vat_rate = data.get('vat_rate')
        default_vat_included = data.get('default_vat_included')
        currency = data.get('currency')

        success = config_manager.set_tax_settings(vat_rate, default_vat_included, currency)

        if success:
            return jsonify({'success': True, 'message': 'Configurazioni IVA salvate'})
        return jsonify({'success': False, 'error': 'Errore nel salvataggio'}), 500

    except Exception as e:
        logger.error("Errore nell'impostazione configurazioni IVA: %s", str(e))
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    # Import già organizzati nella sezione top-level

    print("🚀 AVVIO APP-ROBERTO - MODALITÀ DEBUG ERRORI")
    print("=" * 50)

    try:
        # DISABILITATO: Pulizia all'avvio per preservare file caricati
        # clean_on_startup()  # Commentato per mantenere persistenza file

        # Determina se l'app è in esecuzione in un container Docker
        in_docker = os.environ.get('DOCKER_CONTAINER', False)
        print("🐳 Docker container: %s", in_docker)

        # Verifica route registrate
        print("\n📋 VERIFICA ROUTE REGISTRATE:")
        total_routes = 0
        employees_route_found = False

        for rule in app.url_map.iter_rules():
            total_routes += 1
            if '/api/config/employees' in str(rule.rule):
                employees_route_found = True
                print("✅ TROVATA: %s [%s] -> %s", rule.rule, ', '.join(rule.methods), rule.endpoint)

        print("📊 Totale route registrate: %s", total_routes)

        if not employees_route_found:
            print("❌ ERRORE: Route /api/config/employees NON registrata!")
        else:
            print("✅ Route /api/config/employees registrata correttamente!")

        # Verifica porte disponibili
        print("\n🔍 VERIFICA PORTE DISPONIBILI:")
        ports_to_try = [5001, 5000, 5002, 5003]
        available_ports = []

        for port in ports_to_try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()

            if result != 0:  # Porta disponibile
                available_ports.append(port)
                print("✅ Porta %s: DISPONIBILE", port)
            else:
                print("❌ Porta %s: OCCUPATA", port)

        if not available_ports:
            print("❌ ERRORE CRITICO: Nessuna porta disponibile!")
            sys.exit(1)

        # Avvia Flask sulla prima porta disponibile
        selected_port = available_ports[0]
        print("\n🚀 AVVIO FLASK SU PORTA %s", selected_port)
        print("🌐 URL: http://127.0.0.1:%s", selected_port)
        print("🔧 Health check: http://127.0.0.1:%s/api/health", selected_port)
        print("👥 Employees API: http://127.0.0.1:%s/api/config/employees", selected_port)
        print("=" * 50)

        # Configurazione Flask ottimizzata
        app.run(
            host='127.0.0.1',
            port=selected_port,
            debug=False,
            threaded=True,
            use_reloader=False,
            processes=1
        )

    except Exception as e:
        print("❌ ERRORE CRITICO AVVIO: %s", e)
        # Import già organizzati nella sezione top-level
        traceback.print_exc()
        sys.exit(1)
